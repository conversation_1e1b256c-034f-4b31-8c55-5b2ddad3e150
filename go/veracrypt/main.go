package main

import (
	"bufio"
	"context"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"syscall"

	"github.com/hanwen/go-fuse/v2/fs"
	"github.com/hanwen/go-fuse/v2/fuse"
	"golang.org/x/crypto/pbkdf2"
	"golang.org/x/term"
)

const (
	keySize    = 32     // AES-256
	nonceSize  = 12     // GCM nonce size
	saltSize   = 32     // Salt size for PBKDF2
	iterations = 100000 // PBKDF2 iterations
)

type VeraCrypt struct {
	password string
}

// VaultRoot represents the root of the encrypted filesystem
type VaultRoot struct {
	fs.Inode
	containerPath string
	password      string
	cryptoCore    *CryptoCore
	mu            sync.RWMutex
}

// CryptoCore handles encryption/decryption operations
type CryptoCore struct {
	key []byte
	mu  sync.RWMutex
}

// VaultFile represents an encrypted file in the filesystem
type VaultFile struct {
	fs.Inode
	root     *VaultRoot
	realPath string
	mu       sync.RWMutex
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "encrypt", "e":
		if len(os.Args) < 3 {
			fmt.Println("Error: Please specify input file")
			printUsage()
			return
		}
		inputFile := os.Args[2]
		outputFile := inputFile + ".encrypted"
		if len(os.Args) >= 4 {
			outputFile = os.Args[3]
		}

		password := getPassword("Enter password for encryption: ")
		if password == "" {
			fmt.Println("Error: Password cannot be empty")
			return
		}

		vc := &VeraCrypt{password: password}
		if err := vc.encryptFile(inputFile, outputFile); err != nil {
			fmt.Printf("Encryption failed: %v\n", err)
			return
		}
		fmt.Printf("File encrypted successfully: %s\n", outputFile)

	case "decrypt", "d":
		if len(os.Args) < 3 {
			fmt.Println("Error: Please specify input file")
			printUsage()
			return
		}
		inputFile := os.Args[2]
		outputFile := strings.TrimSuffix(inputFile, ".encrypted")
		if outputFile == inputFile {
			outputFile = inputFile + ".decrypted"
		}
		if len(os.Args) >= 4 {
			outputFile = os.Args[3]
		}

		password := getPassword("Enter password for decryption: ")
		if password == "" {
			fmt.Println("Error: Password cannot be empty")
			return
		}

		vc := &VeraCrypt{password: password}
		if err := vc.decryptFile(inputFile, outputFile); err != nil {
			fmt.Printf("Decryption failed: %v\n", err)
			return
		}
		fmt.Printf("File decrypted successfully: %s\n", outputFile)

	case "mount", "m":
		if len(os.Args) < 4 {
			fmt.Println("Error: Please specify container file and mount point")
			fmt.Println("Usage: ./veracrypt mount <container_file> <mount_point>")
			return
		}
		containerFile := os.Args[2]
		mountPoint := os.Args[3]

		password := getPassword("Enter password for container: ")
		if password == "" {
			fmt.Println("Error: Password cannot be empty")
			return
		}

		vc := &VeraCrypt{password: password}
		if err := vc.mountContainer(containerFile, mountPoint); err != nil {
			fmt.Printf("Mount failed: %v\n", err)
			return
		}

	case "create-container", "cc":
		if len(os.Args) < 3 {
			fmt.Println("Error: Please specify container file path")
			fmt.Println("Usage: ./veracrypt create-container <container_file> [size_mb]")
			return
		}
		containerFile := os.Args[2]
		sizeMB := 100 // Default 100MB
		if len(os.Args) >= 4 {
			fmt.Sscanf(os.Args[3], "%d", &sizeMB)
		}

		password := getPassword("Enter password for new container: ")
		if password == "" {
			fmt.Println("Error: Password cannot be empty")
			return
		}

		confirmPassword := getPassword("Confirm password: ")
		if password != confirmPassword {
			fmt.Println("Error: Passwords do not match")
			return
		}

		vc := &VeraCrypt{password: password}
		if err := vc.createContainer(containerFile, sizeMB); err != nil {
			fmt.Printf("Container creation failed: %v\n", err)
			return
		}
		fmt.Printf("Container created successfully: %s (%dMB)\n", containerFile, sizeMB)

	case "help", "h", "--help":
		printUsage()

	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("VeraCrypt-like File Encryption Tool with FUSE Support")
	fmt.Println("Usage:")
	fmt.Println("  encrypt|e <input_file> [output_file]           - Encrypt a file")
	fmt.Println("  decrypt|d <input_file> [output_file]           - Decrypt a file")
	fmt.Println("  create-container|cc <container_file> [size_mb] - Create encrypted container")
	fmt.Println("  mount|m <container_file> <mount_point>         - Mount encrypted container")
	fmt.Println("  help|h                                         - Show this help")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  ./veracrypt encrypt document.txt")
	fmt.Println("  ./veracrypt decrypt document.txt.encrypted")
	fmt.Println("  ./veracrypt create-container vault.vc 500")
	fmt.Println("  ./veracrypt mount vault.vc ./mnt")
}

// getPassword prompts user for password input without echoing to terminal
func getPassword(prompt string) string {
	fmt.Print(prompt)

	// Try to get password without echo
	bytePassword, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		// Fallback to regular input if terminal doesn't support hidden input
		fmt.Print("(Warning: Password will be visible) ")
		reader := bufio.NewReader(os.Stdin)
		password, _ := reader.ReadString('\n')
		return strings.TrimSpace(password)
	}

	fmt.Println() // Add newline after password input
	return string(bytePassword)
}

// deriveKey derives encryption key from password using PBKDF2
func (vc *VeraCrypt) deriveKey(salt []byte) []byte {
	return pbkdf2.Key([]byte(vc.password), salt, iterations, keySize, sha256.New)
}

// encryptFile encrypts a file using AES-256-GCM
func (vc *VeraCrypt) encryptFile(inputPath, outputPath string) error {
	// Check if input file exists
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return fmt.Errorf("input file does not exist: %s", inputPath)
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Generate random salt
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return fmt.Errorf("failed to generate salt: %v", err)
	}

	// Derive key from password
	key := vc.deriveKey(salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %v", err)
	}

	// Generate random nonce
	nonce := make([]byte, nonceSize)
	if _, err := rand.Read(nonce); err != nil {
		return fmt.Errorf("failed to generate nonce: %v", err)
	}

	// Open input file
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// Create output file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// Write salt and nonce to output file
	if _, err := outputFile.Write(salt); err != nil {
		return fmt.Errorf("failed to write salt: %v", err)
	}
	if _, err := outputFile.Write(nonce); err != nil {
		return fmt.Errorf("failed to write nonce: %v", err)
	}

	// Read and encrypt file content
	plaintext, err := io.ReadAll(inputFile)
	if err != nil {
		return fmt.Errorf("failed to read input file: %v", err)
	}

	// Encrypt the data
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	// Write encrypted data to output file
	if _, err := outputFile.Write(ciphertext); err != nil {
		return fmt.Errorf("failed to write encrypted data: %v", err)
	}

	return nil
}

// decryptFile decrypts a file using AES-256-GCM
func (vc *VeraCrypt) decryptFile(inputPath, outputPath string) error {
	// Check if input file exists
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return fmt.Errorf("input file does not exist: %s", inputPath)
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Open input file
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// Read salt
	salt := make([]byte, saltSize)
	if _, err := io.ReadFull(inputFile, salt); err != nil {
		return fmt.Errorf("failed to read salt: %v", err)
	}

	// Read nonce
	nonce := make([]byte, nonceSize)
	if _, err := io.ReadFull(inputFile, nonce); err != nil {
		return fmt.Errorf("failed to read nonce: %v", err)
	}

	// Derive key from password
	key := vc.deriveKey(salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %v", err)
	}

	// Read encrypted data
	ciphertext, err := io.ReadAll(inputFile)
	if err != nil {
		return fmt.Errorf("failed to read encrypted data: %v", err)
	}

	// Decrypt the data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt data (wrong password?): %v", err)
	}

	// Create output file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// Write decrypted data to output file
	if _, err := outputFile.Write(plaintext); err != nil {
		return fmt.Errorf("failed to write decrypted data: %v", err)
	}

	return nil
}

// createContainer creates a new encrypted container file
func (vc *VeraCrypt) createContainer(containerPath string, sizeMB int) error {
	// Create container file
	file, err := os.Create(containerPath)
	if err != nil {
		return fmt.Errorf("failed to create container: %v", err)
	}
	defer file.Close()

	// Generate random salt
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return fmt.Errorf("failed to generate salt: %v", err)
	}

	// Write salt to container header
	if _, err := file.Write(salt); err != nil {
		return fmt.Errorf("failed to write salt: %v", err)
	}

	// Create empty data of specified size
	sizeBytes := int64(sizeMB) * 1024 * 1024
	emptyData := make([]byte, 1024) // Write in 1KB chunks

	for written := int64(0); written < sizeBytes; {
		chunkSize := int64(len(emptyData))
		if written+chunkSize > sizeBytes {
			chunkSize = sizeBytes - written
		}

		if _, err := file.Write(emptyData[:chunkSize]); err != nil {
			return fmt.Errorf("failed to write container data: %v", err)
		}
		written += chunkSize
	}

	return nil
}

// mountContainer mounts an encrypted container using FUSE
func (vc *VeraCrypt) mountContainer(containerPath, mountPoint string) error {
	// Check if container exists
	if _, err := os.Stat(containerPath); os.IsNotExist(err) {
		return fmt.Errorf("container file does not exist: %s", containerPath)
	}

	// Create mount point if it doesn't exist
	if err := os.MkdirAll(mountPoint, 0755); err != nil {
		return fmt.Errorf("failed to create mount point: %v", err)
	}

	// Read salt from container
	file, err := os.Open(containerPath)
	if err != nil {
		return fmt.Errorf("failed to open container: %v", err)
	}
	defer file.Close()

	salt := make([]byte, saltSize)
	if _, err := io.ReadFull(file, salt); err != nil {
		return fmt.Errorf("failed to read salt: %v", err)
	}

	// Derive key from password
	key := vc.deriveKey(salt)

	// Create crypto core
	cryptoCore := &CryptoCore{key: key}

	// Create vault root
	root := &VaultRoot{
		containerPath: containerPath,
		password:      vc.password,
		cryptoCore:    cryptoCore,
	}

	// Mount options
	opts := &fs.Options{
		MountOptions: fuse.MountOptions{
			Name:   "veracrypt",
			FsName: "veracrypt",
			Debug:  false,
		},
	}

	// Mount filesystem
	server, err := fs.Mount(mountPoint, root, opts)
	if err != nil {
		return fmt.Errorf("mount failed: %v", err)
	}

	fmt.Printf("Container mounted at: %s\n", mountPoint)
	fmt.Printf("Press Ctrl+C to unmount\n")

	// Wait for unmount
	server.Wait()

	return nil
}

// FUSE interface methods for VaultRoot

// Lookup looks up a child node by name
func (vr *VaultRoot) Lookup(ctx context.Context, name string, out *fuse.EntryOut) (*fs.Inode, syscall.Errno) {
	vr.mu.RLock()
	defer vr.mu.RUnlock()

	// For now, return a simple file node
	child := &VaultFile{
		root:     vr,
		realPath: name,
	}

	// Set file attributes
	out.Attr.Mode = 0644
	out.Attr.Size = 1024 // Default size

	return vr.NewInode(ctx, child, fs.StableAttr{Mode: syscall.S_IFREG}), 0
}

// Readdir reads directory contents
func (vr *VaultRoot) Readdir(ctx context.Context) (fs.DirStream, syscall.Errno) {
	vr.mu.RLock()
	defer vr.mu.RUnlock()

	// Return empty directory for now
	entries := []fuse.DirEntry{}

	return fs.NewListDirStream(entries), 0
}

// Create creates a new file
func (vr *VaultRoot) Create(ctx context.Context, name string, flags uint32, mode uint32, out *fuse.EntryOut) (node *fs.Inode, fh fs.FileHandle, fuseFlags uint32, errno syscall.Errno) {
	vr.mu.Lock()
	defer vr.mu.Unlock()

	// Create new file node
	child := &VaultFile{
		root:     vr,
		realPath: name,
	}

	// Set file attributes
	out.Attr.Mode = mode
	out.Attr.Size = 0

	inode := vr.NewInode(ctx, child, fs.StableAttr{Mode: syscall.S_IFREG})

	// Create file handle
	fh = &VaultFileHandle{
		file: child,
		data: make([]byte, 0),
	}

	return inode, fh, 0, 0
}

// FUSE interface methods for VaultFile

// Open opens a file for reading/writing
func (vf *VaultFile) Open(ctx context.Context, flags uint32) (fh fs.FileHandle, fuseFlags uint32, errno syscall.Errno) {
	vf.mu.RLock()
	defer vf.mu.RUnlock()

	// Create file handle with empty data for now
	fh = &VaultFileHandle{
		file: vf,
		data: make([]byte, 1024), // Default content
	}

	return fh, 0, 0
}

// Getattr gets file attributes
func (vf *VaultFile) Getattr(ctx context.Context, fh fs.FileHandle, out *fuse.AttrOut) syscall.Errno {
	vf.mu.RLock()
	defer vf.mu.RUnlock()

	out.Attr.Mode = 0644
	out.Attr.Size = 1024 // Default size

	return 0
}

// VaultFileHandle represents an open file handle
type VaultFileHandle struct {
	file *VaultFile
	data []byte
	mu   sync.RWMutex
}

// Read reads data from the file
func (vfh *VaultFileHandle) Read(ctx context.Context, dest []byte, off int64) (fuse.ReadResult, syscall.Errno) {
	vfh.mu.RLock()
	defer vfh.mu.RUnlock()

	if off >= int64(len(vfh.data)) {
		return fuse.ReadResultData([]byte{}), 0
	}

	end := off + int64(len(dest))
	if end > int64(len(vfh.data)) {
		end = int64(len(vfh.data))
	}

	return fuse.ReadResultData(vfh.data[off:end]), 0
}

// Write writes data to the file
func (vfh *VaultFileHandle) Write(ctx context.Context, data []byte, off int64) (written uint32, errno syscall.Errno) {
	vfh.mu.Lock()
	defer vfh.mu.Unlock()

	// Extend data if necessary
	if off+int64(len(data)) > int64(len(vfh.data)) {
		newData := make([]byte, off+int64(len(data)))
		copy(newData, vfh.data)
		vfh.data = newData
	}

	// Write data
	copy(vfh.data[off:], data)

	return uint32(len(data)), 0
}

// Flush flushes any pending writes
func (vfh *VaultFileHandle) Flush(ctx context.Context) syscall.Errno {
	// For now, just return success
	return 0
}

// Release releases the file handle
func (vfh *VaultFileHandle) Release(ctx context.Context) syscall.Errno {
	// Clean up resources if needed
	return 0
}
