# Makefile for gocryptfs project
# Provides convenient targets for building, testing, and development

.PHONY: help build test test-unit test-integration test-benchmarks test-security test-all clean coverage lint fmt vet deps check install

# Default target
.DEFAULT_GOAL := help

# Variables
BINARY_NAME := gocryptfs
BUILD_DIR := build
COVERAGE_FILE := coverage.out
COVERAGE_HTML := coverage.html

# Go related variables
GOCMD := go
GOBUILD := $(GOCMD) build
GOCLEAN := $(GOCMD) clean
GOTEST := $(GOCMD) test
GOGET := $(GOCMD) get
GOMOD := $(GOCMD) mod
GOFMT := $(GOCMD) fmt
GOVET := $(GOCMD) vet

# Build flags
BUILD_FLAGS := -v
TEST_FLAGS := -v -race -timeout 10m
BENCH_FLAGS := -bench=. -benchmem -benchtime=1s

# Help target
help: ## Show this help message
	@echo "gocryptfs - Encrypted filesystem in Go"
	@echo ""
	@echo "Usage: make [target]"
	@echo ""
	@echo "Targets:"
	@awk 'BEGIN {FS = ":.*?## "} /^[a-zA-Z_-]+:.*?## / {printf "  %-20s %s\n", $$1, $$2}' $(MAKEFILE_LIST)

# Build targets
build: ## Build the main binary
	@echo "Building $(BINARY_NAME)..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(BUILD_FLAGS) -o $(BUILD_DIR)/$(BINARY_NAME) ./

build-debug: ## Build with debug symbols
	@echo "Building $(BINARY_NAME) with debug symbols..."
	@mkdir -p $(BUILD_DIR)
	$(GOBUILD) $(BUILD_FLAGS) -gcflags="all=-N -l" -o $(BUILD_DIR)/$(BINARY_NAME)-debug ./

# Test targets
test: test-unit ## Run default tests (unit tests)

test-unit: ## Run unit tests
	@echo "Running unit tests..."
	$(GOTEST) $(TEST_FLAGS) ./internal/crypto/
	$(GOTEST) $(TEST_FLAGS) ./tests/unit/

test-integration: ## Run integration tests
	@echo "Running integration tests..."
	$(GOTEST) $(TEST_FLAGS) ./tests/integration/

test-benchmarks: ## Run benchmark tests
	@echo "Running benchmark tests..."
	$(GOTEST) $(BENCH_FLAGS) ./tests/benchmarks/

test-security: ## Run security tests
	@echo "Running security tests..."
	$(GOTEST) $(TEST_FLAGS) ./tests/security/

test-all: ## Run all tests
	@echo "Running all tests..."
	./tests/run_tests.sh all -v

test-coverage: ## Run tests with coverage
	@echo "Running tests with coverage..."
	./tests/run_tests.sh all -c -v

test-race: ## Run tests with race detection
	@echo "Running tests with race detection..."
	./tests/run_tests.sh all -r -v

test-quick: ## Run quick tests (no benchmarks)
	@echo "Running quick tests..."
	$(GOTEST) -short $(TEST_FLAGS) ./internal/crypto/ ./tests/unit/ ./tests/integration/ ./tests/security/

# Coverage targets
coverage: test-coverage ## Generate coverage report
	@if [ -f $(COVERAGE_FILE) ]; then \
		echo "Generating coverage report..."; \
		$(GOCMD) tool cover -html=$(COVERAGE_FILE) -o $(COVERAGE_HTML); \
		echo "Coverage report generated: $(COVERAGE_HTML)"; \
		$(GOCMD) tool cover -func=$(COVERAGE_FILE) | tail -1; \
	else \
		echo "No coverage file found. Run 'make test-coverage' first."; \
	fi

coverage-view: coverage ## Open coverage report in browser
	@if [ -f $(COVERAGE_HTML) ]; then \
		echo "Opening coverage report in browser..."; \
		open $(COVERAGE_HTML) || xdg-open $(COVERAGE_HTML) || echo "Please open $(COVERAGE_HTML) manually"; \
	else \
		echo "No coverage report found. Run 'make coverage' first."; \
	fi

# Code quality targets
fmt: ## Format Go code
	@echo "Formatting Go code..."
	$(GOFMT) ./...

vet: ## Run go vet
	@echo "Running go vet..."
	$(GOVET) ./...

lint: ## Run golint (requires golint to be installed)
	@echo "Running golint..."
	@if command -v golint >/dev/null 2>&1; then \
		golint ./...; \
	else \
		echo "golint not found. Install with: go install golang.org/x/lint/golint@latest"; \
	fi

check: fmt vet ## Run all code quality checks
	@echo "All code quality checks completed"

# Dependency management
deps: ## Download dependencies
	@echo "Downloading dependencies..."
	$(GOMOD) download

deps-update: ## Update dependencies
	@echo "Updating dependencies..."
	$(GOMOD) tidy
	$(GOGET) -u ./...

deps-verify: ## Verify dependencies
	@echo "Verifying dependencies..."
	$(GOMOD) verify

# Installation targets
install: build ## Install the binary to GOPATH/bin
	@echo "Installing $(BINARY_NAME)..."
	$(GOCMD) install ./

install-tools: ## Install development tools
	@echo "Installing development tools..."
	$(GOGET) golang.org/x/lint/golint@latest
	$(GOGET) golang.org/x/tools/cmd/goimports@latest

# Cleanup targets
clean: ## Clean build artifacts and test files
	@echo "Cleaning up..."
	$(GOCLEAN)
	@rm -rf $(BUILD_DIR)
	@rm -f $(COVERAGE_FILE) $(COVERAGE_HTML)
	@find . -name "*.test" -delete
	@find . -name "*.out" -delete

clean-all: clean ## Clean everything including dependencies
	@echo "Cleaning all..."
	$(GOMOD) clean -cache

# Development targets
dev-setup: deps install-tools ## Setup development environment
	@echo "Development environment setup completed"

dev-test: ## Run tests in development mode (with race detection and coverage)
	@echo "Running development tests..."
	./tests/run_tests.sh all -r -c -v

dev-bench: ## Run quick benchmarks for development
	@echo "Running development benchmarks..."
	$(GOTEST) -bench=. -benchtime=100ms -benchmem ./tests/benchmarks/ | head -20

# Demo targets
demo: build ## Run a simple demo
	@echo "Running gocryptfs demo..."
	@mkdir -p demo/cipher demo/plain
	@echo "Demo directories created: demo/cipher, demo/plain"
	@echo "Run: $(BUILD_DIR)/$(BINARY_NAME) -init demo/cipher"
	@echo "Then: $(BUILD_DIR)/$(BINARY_NAME) demo/cipher demo/plain"

demo-clean: ## Clean demo directories
	@echo "Cleaning demo directories..."
	@rm -rf demo/

# CI/CD targets
ci-test: deps check test-all ## Run CI tests
	@echo "CI tests completed"

ci-build: deps build ## Run CI build
	@echo "CI build completed"

# Performance targets
perf-test: ## Run performance tests
	@echo "Running performance tests..."
	./tests/run_tests.sh benchmarks -b 5s

perf-profile: ## Run performance profiling
	@echo "Running performance profiling..."
	$(GOTEST) -bench=BenchmarkFileEncryption_MediumFiles -benchmem -cpuprofile=cpu.prof -memprofile=mem.prof ./tests/benchmarks/
	@echo "Profiles generated: cpu.prof, mem.prof"
	@echo "View with: go tool pprof cpu.prof"

# Documentation targets
docs: ## Generate documentation
	@echo "Generating documentation..."
	$(GOCMD) doc -all ./internal/crypto
	$(GOCMD) doc -all ./internal/config
	$(GOCMD) doc -all ./internal/fusefs

# Security targets
security-test: test-security ## Run security tests
	@echo "Security tests completed"

security-audit: ## Run security audit (requires gosec)
	@echo "Running security audit..."
	@if command -v gosec >/dev/null 2>&1; then \
		gosec ./...; \
	else \
		echo "gosec not found. Install with: go install github.com/securecodewarrior/gosec/v2/cmd/gosec@latest"; \
	fi

# Release targets
release-build: clean ## Build release binaries
	@echo "Building release binaries..."
	@mkdir -p $(BUILD_DIR)/release
	GOOS=linux GOARCH=amd64 $(GOBUILD) -ldflags="-s -w" -o $(BUILD_DIR)/release/$(BINARY_NAME)-linux-amd64 ./
	GOOS=darwin GOARCH=amd64 $(GOBUILD) -ldflags="-s -w" -o $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-amd64 ./
	GOOS=darwin GOARCH=arm64 $(GOBUILD) -ldflags="-s -w" -o $(BUILD_DIR)/release/$(BINARY_NAME)-darwin-arm64 ./
	GOOS=windows GOARCH=amd64 $(GOBUILD) -ldflags="-s -w" -o $(BUILD_DIR)/release/$(BINARY_NAME)-windows-amd64.exe ./
	@echo "Release binaries built in $(BUILD_DIR)/release/"

# UI targets
ui-install: ## Install UI dependencies
	@echo "Installing UI dependencies..."
	@cd ui && npm install
	@cd ui/backend && npm install

ui-start: ## Start UI (backend + frontend)
	@echo "Starting gocryptfs UI..."
	@cd ui && ./start-ui.sh

ui-backend: ## Start only backend API server
	@echo "Starting backend API server..."
	@cd ui/backend && npm start

ui-frontend: ## Start only React frontend
	@echo "Starting React frontend..."
	@cd ui && npm start

ui-build: ## Build UI for production
	@echo "Building UI for production..."
	@cd ui && npm run build

ui-clean: ## Clean UI dependencies and build files
	@echo "Cleaning UI files..."
	@rm -rf ui/node_modules ui/backend/node_modules ui/build

ui-test: ## Run UI tests
	@echo "Running UI tests..."
	@cd ui && npm test

# Show project status
status: ## Show project status
	@echo "=== gocryptfs Project Status ==="
	@echo "Go version: $$(go version)"
	@echo "Module: $$(go list -m)"
	@echo "Dependencies:"
	@$(GOMOD) list -m all | head -10
	@echo ""
	@echo "Test coverage:"
	@if [ -f $(COVERAGE_FILE) ]; then \
		$(GOCMD) tool cover -func=$(COVERAGE_FILE) | tail -1; \
	else \
		echo "No coverage data available. Run 'make test-coverage' first."; \
	fi
	@echo ""
	@echo "Build status:"
	@if [ -f $(BUILD_DIR)/$(BINARY_NAME) ]; then \
		echo "Binary exists: $(BUILD_DIR)/$(BINARY_NAME)"; \
		ls -la $(BUILD_DIR)/$(BINARY_NAME); \
	else \
		echo "Binary not built. Run 'make build' first."; \
	fi
	@echo ""
	@echo "UI status:"
	@if [ -d ui/node_modules ]; then \
		echo "UI dependencies installed"; \
	else \
		echo "UI dependencies not installed. Run 'make ui-install' first."; \
	fi
