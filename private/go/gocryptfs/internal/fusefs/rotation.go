package fusefs

import (
	"fmt"
	"gocryptfs/internal/config"
	"gocryptfs/internal/crypto"
	"os"
	"path/filepath"
)

// MigrateOldDatas rotate master key cho tất cả files trong directory
func MigrateOldDatas(newFS *FS, currentDir string) {

	cacheOldFS := make(map[uint16]*FS)

	// Walk through directory để tìm envelope encrypted files
	err := filepath.Walk(currentDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories và special files
		if info.IsDir() || crypto.IsContainFilename(info.Name()) {
			return nil
		}

		// Check if file is envelope encrypted and
		version, should := shouldMigrateFile(path, newFS.CryptoCore.MasterKeyVersion)
		if !should {
			return nil
		}

		// create or get old FS from cache
		oldFS, ok := cacheOldFS[version]
		if !ok {
			oldFS, err = getBackupFSByCipherDir(currentDir, version)
			if err != nil {
				return fmt.Errorf("cannot get old FS for file (%s): %v", info.Name(), err)
			}
			cacheOldFS[version] = oldFS
		}

		// ---------------- do action ----------------
		err = migrateOldData(oldFS, newFS, currentDir, info.Name())
		if err != nil {
			fmt.Printf("failed to rotate key for file (%s): %v", info.Name(), err)
		}

		return nil
	})

	if err != nil {
		fmt.Printf("cannot walk directory: %v", err)
		return
	}

}

// migrateOldData rotate master key cho file tại chỗ (overwrite file)
func migrateOldData(oldFS *FS, newFS *FS, currentDir, fileNameOld string) error {

	filePathOld := filepath.Join(currentDir, fileNameOld)

	// B1: Đọc file hiện tại
	oldCipherText, err := os.ReadFile(filePathOld)
	if err != nil {
		return fmt.Errorf("cannot read file: %v", err)
	}

	// B2: Rotate key ra newCiphertext
	newCiphertext, err := crypto.RotateFileKey(oldFS.CryptoCore, oldCipherText, newFS.CryptoCore.MasterKey, newFS.CryptoCore.MasterKeyVersion)
	if err != nil {
		return err
	}

	// B3: Dịch tên file cũ thành plain text
	plainName, err := oldFS.CryptoCore.DecryptFilename(fileNameOld, currentDir)
	if err != nil {
		return fmt.Errorf("cannot decrypt filename: %v", err)
	}

	// =======================

	// B4: chuyển plainName thành encryptFilename
	encryptFilename, err := newFS.CryptoCore.EncryptFilename(plainName, currentDir)
	if err != nil {
		return fmt.Errorf("cannot encrypt filename: %v", err)
	}
	newFilePath := filepath.Join(currentDir, encryptFilename)

	// =======================

	// B5: Write newCiphertext vào newFilePath
	if err := os.WriteFile(newFilePath, newCiphertext, 0644); err != nil {
		return fmt.Errorf("cannot write rotated file: %v", err)
	}

	// B6: Backup file trước khi overwrite
	backupPath := fmt.Sprintf("%v/_backup.%v.%v", currentDir, oldFS.CryptoCore.MasterKeyVersion, fileNameOld)
	if err := os.Rename(filePathOld, backupPath); err != nil {
		return fmt.Errorf("cannot rename file: %v", err)
	}

	return nil
}

// shouldMigrateFile kiểm tra xem file có cần migrate không
func shouldMigrateFile(filePath string, currentMasterKeyVersion uint16) (uint16, bool) {

	cipherData, err := os.ReadFile(filePath)
	if err != nil || len(cipherData) < 2 {
		return 0, false
	}
	if len(cipherData) < 4 {
		return 0, false
	}

	// Parse header để check master key version
	if len(cipherData) < crypto.ENVELOPE_HEADER_SIZE {
		return 0, false
	}

	header, err := crypto.ParseEnvelopeFileHeader(cipherData[:crypto.ENVELOPE_HEADER_SIZE])
	if err != nil {
		return 0, false
	}

	// Migrate nếu file đang dùng master key version cũ
	return header.MasterKeyVersion, header.MasterKeyVersion > 0 && header.MasterKeyVersion < currentMasterKeyVersion
}

// ================================================================================

// readPassword đọc password từ terminal
func readPassword(prompt string) (string, error) {
	fmt.Print(prompt)

	// TODO: Implement proper password hiding
	// For now, just read normally (password will be visible)
	var password string
	fmt.Scanln(&password)

	return password, nil
}

func getBackupFSByCipherDir(cipherDir string, version uint16) (*FS, error) {
	backupPath := fmt.Sprintf("%v/_backup.%d.%v", cipherDir, version, crypto.CONFIG_FILENAME)

	// Đọc password cũ từ người dùng
	password, err := readPassword("Enter old password: ")
	if err != nil {
		return nil, fmt.Errorf("cannot read password: %v", err)
	}

	// Đọc config
	conf, err := config.LoadConfig(backupPath)
	if err != nil {
		return nil, fmt.Errorf("cannot load config: %v", err)
	}

	// Verify password cũ và lấy master key cũ
	oldMasterKey, err := conf.DecryptMasterKey(password)
	if err != nil {
		return nil, fmt.Errorf("wrong old password or corrupted config file")
	}

	// oldFS sẽ là filesystem với master key cũ
	backupConf, err := config.LoadConfig(backupPath)
	if err != nil {
		return nil, fmt.Errorf("cannot load config: %v", err)
	}
	oldFS, err := NewFSWithConfig(backupPath, oldMasterKey, backupConf, false)
	if err != nil {
		return nil, fmt.Errorf("cannot create filesystem: %v", err)
	}

	return oldFS, nil
}
