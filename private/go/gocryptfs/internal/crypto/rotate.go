package crypto

import "fmt"

// RotateFileKey rotate master key cho một file envelope encrypted
func RotateFile<PERSON>ey(cc *CryptoCore, ciphertext []byte, newMasterKey []byte, newMasterKeyVersion uint16) ([]byte, error) {
	if len(ciphertext) < ENVELOPE_HEADER_SIZE {
		return nil, fmt.Errorf("file too short for envelope encryption: %d bytes", len(ciphertext))
	}

	// Parse header hiện tại
	header, err := ParseEnvelopeFileHeader(ciphertext[:ENVELOPE_HEADER_SIZE])
	if err != nil {
		return nil, fmt.Errorf("cannot parse envelope header: %v", err)
	}

	// Giải mã DEK bằng master key cũ
	dek, err := cc.decryptDEK(header.EncryptedDEK, header.DEKIV)
	if err != nil {
		return nil, fmt.<PERSON><PERSON><PERSON>("cannot decrypt DEK with old master key: %v", err)
	}

	// Tạo crypto core mới với master key mới
	newCC, err := NewCryptoCore(newMasterKey, newMasterKeyVersion)
	if err != nil {
		return nil, fmt.Errorf("cannot create new crypto core: %v", err)
	}

	// Mã hóa DEK bằng master key mới
	newEncryptedDEK, err := newCC.encryptDEK(dek, header.DEKIV)
	if err != nil {
		return nil, fmt.Errorf("cannot encrypt DEK with new master key: %v", err)
	}

	// Tạo header mới
	newHeader := &EnvelopeFileHeader{
		Version:          header.Version,
		MasterKeyVersion: newMasterKeyVersion,
		FileID:           header.FileID,
		EncryptedDEK:     newEncryptedDEK,
		DEKIV:            header.DEKIV,
	}

	// Serialize header mới
	newHeaderBytes := newHeader.SerializeEnvelopeFileHeader()

	// Tạo file mới với header mới + data cũ (data không thay đổi!)
	newCiphertext := make([]byte, len(ciphertext))
	copy(newCiphertext[:ENVELOPE_HEADER_SIZE], newHeaderBytes)
	copy(newCiphertext[ENVELOPE_HEADER_SIZE:], ciphertext[ENVELOPE_HEADER_SIZE:])

	return newCiphertext, nil
}
