{"name": "gocryptfs-controller-backend", "version": "0.1.0", "description": "Backend API server for gocryptfs controller", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "body-parser": "^1.20.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "keywords": ["gocryptfs", "encryption", "filesystem", "controller", "api"], "author": "", "license": "MIT"}