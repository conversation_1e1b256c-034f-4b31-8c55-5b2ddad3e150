package fusefs

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"syscall"

	"github.com/hanwen/go-fuse/v2/fs"
	"github.com/hanwen/go-fuse/v2/fuse"

	"gocryptfs/internal/config"
	"gocryptfs/internal/crypto"
)

// FS là root filesystem node
type FS struct {
	// CipherDir là thư mục chứa dữ liệu mã hóa
	CipherDir string
	// CryptoCore để mã hóa/giải mã
	CryptoCore *crypto.CryptoCore
	// Debug mode
	Debug bool
}

// NewFS tạo một filesystem mới
func NewFS(cipherDir string, masterKey []byte, newMasterKeyVersion uint16, debug bool) (*FS, error) {
	// Tạo crypto core
	cc, err := crypto.NewCryptoCore(masterKey, newMasterKeyVersion)
	if err != nil {
		return nil, fmt.Errorf("cannot create crypto core: %v", err)
	}

	return &FS{
		CipherDir:  cipherDir,
		CryptoCore: cc,
		Debug:      debug,
	}, nil
}

// NewFSWithConfig tạo filesystem mới với config
func NewFSWithConfig(cipherDir string, masterKey []byte, cfg *config.Config, debug bool) (*FS, error) {
	// Tạo crypto core
	cc, err := crypto.NewCryptoCore(masterKey, cfg.MasterKeyVersion)
	if err != nil {
		return nil, fmt.Errorf("cannot create crypto core: %v", err)
	}

	return &FS{
		CipherDir:  cipherDir,
		CryptoCore: cc,
		Debug:      debug,
	}, nil
}

// Mount mount filesystem tại mountpoint
func (f *FS) Mount(mountpoint string) error {
	// Tạo root node
	root := &Node{
		fs:         f,
		cipherPath: f.CipherDir,
		plainPath:  "/",
	}

	// Mount options
	opts := &fs.Options{
		MountOptions: fuse.MountOptions{
			Name:   "gocryptfs",
			FsName: "gocryptfs",
			Debug:  f.Debug,
		},
	}

	// Mount
	server, err := fs.Mount(mountpoint, root, opts)
	if err != nil {
		return fmt.Errorf("mount failed: %v", err)
	}

	fmt.Printf("Filesystem mounted at %s\n", mountpoint)
	fmt.Printf("Press Ctrl+C to unmount\n")

	// Wait for unmount
	server.Wait()

	return nil
}

// Node đại diện cho một file hoặc directory trong filesystem
type Node struct {
	fs.Inode

	// fs là reference tới filesystem
	fs *FS
	// cipherPath là đường dẫn trong cipher directory
	cipherPath string
	// plainPath là đường dẫn trong plain filesystem
	plainPath string
}

// Getattr trả về attributes của file/directory
func (n *Node) Getattr(ctx context.Context, fh fs.FileHandle, out *fuse.AttrOut) syscall.Errno {
	if n.fs.Debug {
		fmt.Printf("Getattr: %s\n", n.plainPath)
	}

	// Lấy stat của cipher file
	stat, err := os.Stat(n.cipherPath)
	if err != nil {
		if os.IsNotExist(err) {
			return syscall.ENOENT
		}
		return syscall.EIO
	}

	// Convert stat to fuse attributes
	out.Mode = uint32(stat.Mode())
	// if stat.IsDir() {
	// 	// Directory permissions: readable, writable, executable for owner
	// 	out.Mode = fuse.S_IFDIR | 0755
	// } else {
	// 	// File permissions: readable, writable for owner
	// 	out.Mode = fuse.S_IFREG | 0644
	// }
	out.Size = uint64(stat.Size())
	out.Mtime = uint64(stat.ModTime().Unix())
	out.Atime = uint64(stat.ModTime().Unix())
	out.Ctime = uint64(stat.ModTime().Unix())

	// Nếu là file thường, cần adjust size
	if stat.Mode().IsRegular() {
		// Đọc file để tính plain size
		cipherDataBytes, err := os.ReadFile(n.cipherPath)
		if err != nil {
			return syscall.EIO
		}

		// Determine header size based on file version
		headerSize := crypto.FILE_HEADER_SIZE
		if len(cipherDataBytes) >= 2 {
			version := uint16(cipherDataBytes[0]) | uint16(cipherDataBytes[1])<<8
			if version == 3 {
				headerSize = crypto.ENVELOPE_HEADER_SIZE
			}
		}

		// Nếu file rỗng hoặc chỉ có header
		if len(cipherDataBytes) <= headerSize {
			out.Size = 0
		} else {
			// Tính plain size từ cipher size
			// Mỗi block có thêm TAG_SIZE bytes
			cipherSize := len(cipherDataBytes) - headerSize
			numBlocks := (cipherSize + crypto.BLOCK_SIZE + crypto.TAG_SIZE - 1) / (crypto.BLOCK_SIZE + crypto.TAG_SIZE)

			// Estimate plain size (có thể không chính xác 100%)
			plainSize := uint64(cipherSize - numBlocks*crypto.TAG_SIZE)
			out.Size = plainSize
		}
	}

	return 0
}

// Access kiểm tra permissions
func (n *Node) Access(ctx context.Context, mask uint32) syscall.Errno {
	if n.fs.Debug {
		fmt.Printf("Access: %s mask=%o\n", n.plainPath, mask)
	}

	// Lấy stat của cipher file để kiểm tra tồn tại
	_, err := os.Stat(n.cipherPath)
	if err != nil {
		if os.IsNotExist(err) {
			return syscall.ENOENT
		}
		return syscall.EIO
	}

	// Luôn cho phép access cho owner
	// Trong thực tế, bạn có thể muốn kiểm tra user ID và permissions
	return 0
}

// Lookup tìm kiếm child node
func (n *Node) Lookup(ctx context.Context, plainName string, out *fuse.EntryOut) (*fs.Inode, syscall.Errno) {
	if n.fs.Debug {
		fmt.Printf("Lookup: %s in %s\n", plainName, n.plainPath)
	}

	// Mã hóa tên file
	cipherName, err := n.fs.CryptoCore.EncryptFilename(plainName, n.cipherPath)
	if err != nil {
		if n.fs.Debug {
			fmt.Printf("Lookup: cannot encrypt filename %s: %v\n", plainName, err)
		}
		return nil, syscall.EIO
	}

	// Tạo đường dẫn cipher
	cipherChildPath := filepath.Join(n.cipherPath, cipherName)
	plainChildPath := filepath.Join(n.plainPath, plainName)

	// Kiểm tra file tồn tại
	stat, err := os.Stat(cipherChildPath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, syscall.ENOENT
		}
		return nil, syscall.EIO
	}

	// Tạo child node
	child := &Node{
		fs:         n.fs,
		cipherPath: cipherChildPath,
		plainPath:  plainChildPath,
	}

	// Tạo inode
	var childInode *fs.Inode
	if stat.IsDir() {
		childInode = n.NewInode(ctx, child, fs.StableAttr{Mode: fuse.S_IFDIR})
	} else {
		childInode = n.NewInode(ctx, child, fs.StableAttr{Mode: fuse.S_IFREG})
	}

	return childInode, 0
}

// Readdir đọc nội dung directory
func (n *Node) Readdir(ctx context.Context) (fs.DirStream, syscall.Errno) {
	if n.fs.Debug {
		fmt.Printf("Readdir: %s\n", n.plainPath)
	}

	// Đọc cipher directory
	entries, err := os.ReadDir(n.cipherPath)
	if err != nil {
		return nil, syscall.EIO
	}

	// Convert to fuse entries
	var fuseEntries []fuse.DirEntry
	for _, entry := range entries {
		// Skip gocryptfs.conf và gocryptfs.diriv
		if crypto.IsContainFilename(entry.Name()) {
			continue
		}

		// Giải mã tên file
		plainName, err := n.fs.CryptoCore.DecryptFilename(entry.Name(), n.cipherPath)
		if err != nil {
			if n.fs.Debug {
				fmt.Printf("Readdir: cannot decrypt filename %s: %v\n", entry.Name(), err)
			}
			continue // Skip files that can't be decrypted
		}

		var mode uint32
		if entry.IsDir() {
			mode = fuse.S_IFDIR
		} else {
			mode = fuse.S_IFREG
		}

		fuseEntries = append(fuseEntries, fuse.DirEntry{
			Name: plainName,
			Mode: mode,
		})
	}

	return fs.NewListDirStream(fuseEntries), 0
}

// Open mở file để đọc/ghi
func (n *Node) Open(ctx context.Context, flags uint32) (fs.FileHandle, uint32, syscall.Errno) {
	if n.fs.Debug {
		fmt.Printf("Open: %s (flags: %x)\n", n.plainPath, flags)
	}

	// Tạo file handle
	fh := &FileHandle{
		node:  n,
		flags: flags,
	}

	return fh, 0, 0
}

// Create tạo file mới
func (n *Node) Create(ctx context.Context, plainName string, flags uint32, mode uint32, out *fuse.EntryOut) (node *fs.Inode, fh fs.FileHandle, fuseFlags uint32, errno syscall.Errno) {
	if n.fs.Debug {
		fmt.Printf("Create: %s in %s\n", plainName, n.plainPath)
	}

	// Mã hóa tên file
	cipherName, err := n.fs.CryptoCore.EncryptFilename(plainName, n.cipherPath)
	if err != nil {
		if n.fs.Debug {
			fmt.Printf("Create: cannot encrypt filename %s: %v\n", plainName, err)
		}
		return nil, nil, 0, syscall.EIO
	}

	// Tạo đường dẫn
	cipherChildPath := filepath.Join(n.cipherPath, cipherName)
	plainChildPath := filepath.Join(n.plainPath, plainName)

	// Tạo file rỗng trong cipher directory
	file, err := os.OpenFile(cipherChildPath, int(flags)|os.O_CREATE, os.FileMode(mode))
	if err != nil {
		return nil, nil, 0, syscall.EIO
	}

	// Tạo file header với encryption mode phù hợp
	headerBytes, err := n.fs.CryptoCore.CreateFileHeaderWithSerialize()
	if err != nil {
		file.Close()
		os.Remove(cipherChildPath)
		return nil, nil, 0, syscall.EIO
	}

	if _, err := file.Write(headerBytes); err != nil {
		file.Close()
		os.Remove(cipherChildPath)
		return nil, nil, 0, syscall.EIO
	}

	file.Close()

	// Tạo child node
	child := &Node{
		fs:         n.fs,
		cipherPath: cipherChildPath,
		plainPath:  plainChildPath,
	}

	// Tạo inode
	childInode := n.NewInode(ctx, child, fs.StableAttr{Mode: fuse.S_IFREG})

	// Tạo file handle
	fileHandle := &FileHandle{
		node:  child,
		flags: flags,
	}

	return childInode, fileHandle, 0, 0
}

// Mkdir tạo directory mới
func (n *Node) Mkdir(ctx context.Context, plainName string, mode uint32, out *fuse.EntryOut) (*fs.Inode, syscall.Errno) {
	if n.fs.Debug {
		fmt.Printf("Mkdir: %s in %s\n", plainName, n.plainPath)
	}

	// Mã hóa tên directory
	cipherName, err := n.fs.CryptoCore.EncryptFilename(plainName, n.cipherPath)
	if err != nil {
		if n.fs.Debug {
			fmt.Printf("Mkdir: cannot encrypt dirname %s: %v\n", plainName, err)
		}
		return nil, syscall.EIO
	}

	// Tạo đường dẫn
	cipherChildPath := filepath.Join(n.cipherPath, cipherName)
	plainChildPath := filepath.Join(n.plainPath, plainName)

	// Tạo directory
	if err := os.Mkdir(cipherChildPath, os.FileMode(mode)); err != nil {
		return nil, syscall.EIO
	}

	// Tạo child node
	child := &Node{
		fs:         n.fs,
		cipherPath: cipherChildPath,
		plainPath:  plainChildPath,
	}

	// Tạo inode
	childInode := n.NewInode(ctx, child, fs.StableAttr{Mode: fuse.S_IFDIR})

	return childInode, 0
}

// // Mknod tạo file node (được gọi bởi touch, etc.)
// func (n *Node) Mknod(ctx context.Context, plainName string, mode uint32, dev uint32, out *fuse.EntryOut) (*fs.Inode, syscall.Errno) {
// 	if n.fs.Debug {
// 		fmt.Printf("Mknod: %s in %s (mode: %o)\n", plainName, n.plainPath, mode)
// 	}

// 	// Chỉ hỗ trợ regular files
// 	if mode&fuse.S_IFREG == 0 {
// 		return nil, syscall.ENOTSUP
// 	}

// 	// Tạo đường dẫn
// 	cipherChildPath := filepath.Join(n.cipherPath, plainName)
// 	plainChildPath := filepath.Join(n.plainPath, plainName)

// 	// Tạo file rỗng trong cipher directory
// 	file, err := os.OpenFile(cipherChildPath, os.O_CREATE|os.O_WRONLY, os.FileMode(mode&0777))
// 	if err != nil {
// 		if n.fs.Debug {
// 			fmt.Printf("Mknod error creating file: %v\n", err)
// 		}
// 		return nil, syscall.EIO
// 	}

// 	// Tạo file header và ghi vào file
// 	header, err := n.fs.CryptoCore.CreateFileHeader()
// 	if err != nil {
// 		file.Close()
// 		os.Remove(cipherChildPath)
// 		if n.fs.Debug {
// 			fmt.Printf("Mknod error creating header: %v\n", err)
// 		}
// 		return nil, syscall.EIO
// 	}

// 	headerBytes := header.SerializeFileHeader()
// 	if _, err := file.Write(headerBytes); err != nil {
// 		file.Close()
// 		os.Remove(cipherChildPath)
// 		if n.fs.Debug {
// 			fmt.Printf("Mknod error writing header: %v\n", err)
// 		}
// 		return nil, syscall.EIO
// 	}

// 	file.Close()

// 	// Tạo child node
// 	child := &Node{
// 		fs:         n.fs,
// 		cipherPath: cipherChildPath,
// 		plainPath:  plainChildPath,
// 	}

// 	// Tạo inode
// 	childInode := n.NewInode(ctx, child, fs.StableAttr{Mode: fuse.S_IFREG})

// 	if n.fs.Debug {
// 		fmt.Printf("Mknod success: %s\n", plainName)
// 	}

// 	return childInode, 0
// }

// Unlink xóa file
func (n *Node) Unlink(ctx context.Context, plainName string) syscall.Errno {
	if n.fs.Debug {
		fmt.Printf("Unlink: %s in %s\n", plainName, n.plainPath)
	}

	// Mã hóa tên file
	cipherName, err := n.fs.CryptoCore.EncryptFilename(plainName, n.cipherPath)
	if err != nil {
		if n.fs.Debug {
			fmt.Printf("Unlink: cannot encrypt filename %s: %v\n", plainName, err)
		}
		return syscall.EIO
	}

	cipherPath := filepath.Join(n.cipherPath, cipherName)
	if err := os.Remove(cipherPath); err != nil {
		if os.IsNotExist(err) {
			return syscall.ENOENT
		}
		return syscall.EIO
	}

	return 0
}

// Rmdir xóa directory
func (n *Node) Rmdir(ctx context.Context, plainName string) syscall.Errno {
	if n.fs.Debug {
		fmt.Printf("Rmdir: %s in %s\n", plainName, n.plainPath)
	}

	// Mã hóa tên directory
	cipherName, err := n.fs.CryptoCore.EncryptFilename(plainName, n.cipherPath)
	if err != nil {
		if n.fs.Debug {
			fmt.Printf("Rmdir: cannot encrypt dirname %s: %v\n", plainName, err)
		}
		return syscall.EIO
	}

	cipherPath := filepath.Join(n.cipherPath, cipherName)
	if err := os.Remove(cipherPath); err != nil {
		if os.IsNotExist(err) {
			return syscall.ENOENT
		}
		return syscall.EIO
	}

	return 0
}

// Setattr set file attributes
func (n *Node) Setattr(ctx context.Context, fh fs.FileHandle, in *fuse.SetAttrIn, out *fuse.AttrOut) syscall.Errno {
	if n.fs.Debug {
		fmt.Printf("Setattr: %s (valid: %x)\n", n.plainPath, in.Valid)
	}

	// Lấy stat hiện tại
	stat, err := os.Stat(n.cipherPath)
	if err != nil {
		if os.IsNotExist(err) {
			return syscall.ENOENT
		}
		return syscall.EIO
	}

	// Xử lý thay đổi size cho regular files
	if in.Valid&fuse.FATTR_SIZE != 0 && stat.Mode().IsRegular() {
		newSize := in.Size

		if n.fs.Debug {
			fmt.Printf("Setattr: truncate %s to size %d\n", n.plainPath, newSize)
		}

		// Đọc dữ liệu hiện tại
		var plainDataBytes []byte
		if stat.Size() > 0 {
			cipherDataBytes, err := os.ReadFile(n.cipherPath)
			if err != nil {
				return syscall.EIO
			}

			plainDataBytes, err = n.fs.CryptoCore.DecryptFileEnvelope(cipherDataBytes)
			if err != nil {
				return syscall.EIO
			}
		}

		// Truncate hoặc extend
		if newSize > uint64(len(plainDataBytes)) {
			// Extend với zeros
			newPlainData := make([]byte, newSize)
			copy(newPlainData, plainDataBytes)
			plainDataBytes = newPlainData
		} else {
			// Truncate
			plainDataBytes = plainDataBytes[:newSize]
		}

		// Mã hóa và ghi lại
		cipherDataBytes, err := n.fs.CryptoCore.EncryptFileEnvelope(plainDataBytes, n.fs.CryptoCore.MasterKeyVersion)
		if err != nil {
			return syscall.EIO
		}

		if err := os.WriteFile(n.cipherPath, cipherDataBytes, stat.Mode()); err != nil {
			return syscall.EIO
		}
	}

	// Xử lý thay đổi timestamps (atime, mtime, ctime)
	// Chúng ta chỉ cần acknowledge request, không cần thực sự thay đổi
	// vì cipher file sẽ có timestamps riêng
	if in.Valid&(fuse.FATTR_ATIME|fuse.FATTR_MTIME|fuse.FATTR_CTIME) != 0 {
		if n.fs.Debug {
			fmt.Printf("Setattr: updating timestamps for %s\n", n.plainPath)
		}
		// Không cần làm gì, chỉ cần trả về OK
	}

	// Trả về attributes mới
	return n.Getattr(ctx, fh, out)
}

// FileHandle xử lý các operations trên file đã mở
type FileHandle struct {
	node  *Node
	flags uint32
}

// Read đọc dữ liệu từ file
func (fh *FileHandle) Read(ctx context.Context, dest []byte, off int64) (fuse.ReadResult, syscall.Errno) {
	if fh.node.fs.Debug {
		fmt.Printf("Read: %s offset=%d size=%d\n", fh.node.plainPath, off, len(dest))
	}

	// Đọc toàn bộ cipher file
	cipherDataBytes, err := os.ReadFile(fh.node.cipherPath)
	if err != nil {
		return nil, syscall.EIO
	}

	// Giải mã với auto-detection
	plainData, err := fh.node.fs.DecryptFileData(cipherDataBytes)
	if err != nil {
		return nil, syscall.EIO
	}

	// Trả về phần dữ liệu được yêu cầu
	if off >= int64(len(plainData)) {
		return fuse.ReadResultData([]byte{}), 0
	}

	end := off + int64(len(dest))
	if end > int64(len(plainData)) {
		end = int64(len(plainData))
	}

	result := plainData[off:end]
	return fuse.ReadResultData(result), 0
}

// Write ghi dữ liệu vào file
func (fh *FileHandle) Write(ctx context.Context, data []byte, off int64) (written uint32, errno syscall.Errno) {
	if fh.node.fs.Debug {
		fmt.Printf("Write: %s offset=%d size=%d\n", fh.node.plainPath, off, len(data))
	}

	// Đọc dữ liệu hiện tại
	var plainDataBytes []byte
	if stat, err := os.Stat(fh.node.cipherPath); err == nil && stat.Size() > 0 {
		cipherDataBytes, err := os.ReadFile(fh.node.cipherPath)
		if err != nil {
			return 0, syscall.EIO
		}

		plainDataBytes, err = fh.node.fs.DecryptFileData(cipherDataBytes)
		if err != nil {
			return 0, syscall.EIO
		}
	}

	// Mở rộng dữ liệu nếu cần
	newSize := off + int64(len(data))
	if newSize > int64(len(plainDataBytes)) {
		newPlainData := make([]byte, newSize)
		copy(newPlainData, plainDataBytes)
		plainDataBytes = newPlainData
	}

	// Ghi dữ liệu mới
	copy(plainDataBytes[off:], data)

	// Mã hóa và ghi vào file với encryption mode phù hợp
	cipherDataBytes, err := fh.node.fs.CryptoCore.EncryptFileEnvelope(plainDataBytes, fh.node.fs.CryptoCore.MasterKeyVersion)
	if err != nil {
		return 0, syscall.EIO
	}

	if err := os.WriteFile(fh.node.cipherPath, cipherDataBytes, 0644); err != nil {
		return 0, syscall.EIO
	}

	return uint32(len(data)), 0
}

// Flush đảm bảo dữ liệu được ghi xuống disk
func (fh *FileHandle) Flush(ctx context.Context) syscall.Errno {
	if fh.node.fs.Debug {
		fmt.Printf("Flush: %s\n", fh.node.plainPath)
	}

	// Không cần làm gì đặc biệt vì chúng ta đã ghi trực tiếp
	return 0
}

// Release đóng file handle
func (fh *FileHandle) Release(ctx context.Context) syscall.Errno {
	if fh.node.fs.Debug {
		fmt.Printf("Release: %s\n", fh.node.plainPath)
	}

	return 0
}

func (fh *Node) Rename(ctx context.Context, name string, newParent fs.InodeEmbedder, newName string, flags uint32) syscall.Errno {
	if fh.fs.Debug {
		fmt.Printf("Rename: %s to %s\n", name, newName)
	}

	cipherOldName, err := fh.fs.CryptoCore.EncryptFilename(name, fh.cipherPath)
	if err != nil {
		if fh.fs.Debug {
			fmt.Printf("Rename: cannot encrypt old filename %s: %v\n", name, err)
		}
		return syscall.EIO
	}

	// Mã hóa tên file mới
	cipherNewName, err := fh.fs.CryptoCore.EncryptFilename(newName, fh.cipherPath)
	if err != nil {
		if fh.fs.Debug {
			fmt.Printf("Rename: cannot encrypt new filename %s: %v\n", newName, err)
		}
		return syscall.EIO
	}

	// Tạo đường dẫn mới
	newCipherPath := filepath.Join(fh.cipherPath, cipherNewName)
	oldCipherPath := filepath.Join(fh.cipherPath, cipherOldName)

	// Thực hiện rename
	if err := os.Rename(oldCipherPath, newCipherPath); err != nil {
		if os.IsNotExist(err) {
			return syscall.ENOENT
		}
		return syscall.EIO
	}

	return 0
}

// ===================== Envelope Encryption Support =====================

// DecryptFileData giải mã file data với encryption mode phù hợp (exported for testing)
func (f *FS) DecryptFileData(ciphertext []byte) ([]byte, error) {
	// Auto-detect encryption mode từ file header
	if len(ciphertext) < crypto.FILE_HEADER_SIZE {
		return nil, fmt.Errorf("file too short")
	}

	// Check version trong header để determine encryption mode
	version := uint16(ciphertext[0]) | uint16(ciphertext[1])<<8

	if version >= 1 {
		return f.CryptoCore.DecryptFileEnvelope(ciphertext)
	} else {
		return nil, fmt.Errorf("unsupported file version: %d", version)
	}
}
