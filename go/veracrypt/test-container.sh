#!/bin/bash

echo "=== Testing VeraCrypt-like Tool with FUSE ==="

# Test 1: Create container
echo "1. Creating encrypted container..."
echo -e "test123\ntest123" | ./veracrypt create-container test-vault.vc 10

if [ $? -eq 0 ]; then
    echo "✅ Container created successfully"
    ls -lh test-vault.vc
else
    echo "❌ Container creation failed"
    exit 1
fi

# Test 2: Create mount point
echo -e "\n2. Creating mount point..."
mkdir -p ./mnt
echo "✅ Mount point created"

# Test 3: Test file encryption/decryption first
echo -e "\n3. Testing file encryption..."
echo "This is a secret message!" > secret.txt
echo "test123" | ./veracrypt encrypt secret.txt

if [ $? -eq 0 ]; then
    echo "✅ File encrypted successfully"
    ls -lh secret.txt.encrypted
else
    echo "❌ File encryption failed"
fi

echo -e "\n4. Testing file decryption..."
echo "test123" | ./veracrypt decrypt secret.txt.encrypted

if [ $? -eq 0 ]; then
    echo "✅ File decrypted successfully"
    echo "Decrypted content:"
    cat secret.txt.decrypted
else
    echo "❌ File decryption failed"
fi

echo -e "\n=== Test completed ==="
echo "Note: FUSE mounting requires manual testing due to interactive nature"
echo "To test mounting, run:"
echo "  ./veracrypt mount test-vault.vc ./mnt"
