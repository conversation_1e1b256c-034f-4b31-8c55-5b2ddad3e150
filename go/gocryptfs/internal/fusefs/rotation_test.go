package fusefs

import (
	"bytes"
	"crypto/rand"
	"gocryptfs/internal/crypto"
	"testing"
	"time"
)

// BenchmarkDEKOnly_KeyRotation đo performance key rotation
func BenchmarkDEKOnly_KeyRotation(b *testing.B) {
	oldMasterKey := make([]byte, 32)
	newMasterKey := make([]byte, 32)
	rand.Read(oldMasterKey)
	rand.Read(newMasterKey)

	cc, err := crypto.NewCryptoCore(oldMasterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	// Test with different file sizes
	testSizes := []struct {
		name string
		size int
	}{
		{"1KB", 1024},
		{"100KB", 100 * 1024},
		{"1MB", 1024 * 1024},
	}

	for _, ts := range testSizes {
		b.Run(ts.name, func(b *testing.B) {
			plaintext := make([]byte, ts.size)
			rand.Read(plaintext)

			ciphertext, err := cc.EncryptFileEnvelope(plaintext, 1)
			if err != nil {
				b.Fatalf("Cannot encrypt test data: %v", err)
			}

			b.<PERSON>setTimer()
			b.SetBytes(int64(ts.size))
			for i := 0; i < b.N; i++ {
				_, err := crypto.RotateFileKey(cc, ciphertext, newMasterKey, 2)
				if err != nil {
					b.Fatalf("Key rotation failed: %v", err)
				}
			}
		})
	}
}

// ==================== Key Rotation Tests ====================

func TestKeyRotation_Basic(t *testing.T) {
	oldMasterKey := make([]byte, 32)
	newMasterKey := make([]byte, 32)
	rand.Read(oldMasterKey)
	rand.Read(newMasterKey)

	cc, err := crypto.NewCryptoCore(oldMasterKey, 1)
	if err != nil {
		t.Fatalf("Cannot create crypto core: %v", err)
	}

	plaintext := []byte("This is test data for key rotation.")
	oldVersion := uint16(1)
	newVersion := uint16(2)

	// Encrypt with old master key
	ciphertext, err := cc.EncryptFileEnvelope(plaintext, oldVersion)
	if err != nil {
		t.Fatalf("Encryption failed: %v", err)
	}

	// Rotate key
	rotatedCiphertext, err := crypto.RotateFileKey(cc, ciphertext, newMasterKey, newVersion)
	if err != nil {
		t.Fatalf("Key rotation failed: %v", err)
	}

	// Verify header updated
	newHeader, _ := crypto.ParseEnvelopeFileHeader(rotatedCiphertext[:crypto.ENVELOPE_HEADER_SIZE])
	if newHeader.MasterKeyVersion != newVersion {
		t.Fatalf("Master key version not updated: expected %d, got %d", newVersion, newHeader.MasterKeyVersion)
	}

	// Verify data blocks unchanged
	if len(ciphertext) != len(rotatedCiphertext) {
		t.Fatalf("File size changed after rotation: %d -> %d", len(ciphertext), len(rotatedCiphertext))
	}

	oldDataBlocks := ciphertext[crypto.ENVELOPE_HEADER_SIZE:]
	newDataBlocks := rotatedCiphertext[crypto.ENVELOPE_HEADER_SIZE:]
	if !bytes.Equal(oldDataBlocks, newDataBlocks) {
		t.Fatal("Data blocks changed after rotation - should remain the same")
	}

	// Verify can't decrypt with old key
	_, err = cc.DecryptFileEnvelope(rotatedCiphertext)
	if err == nil {
		t.Fatal("Should not be able to decrypt with old master key")
	}

	// Verify can decrypt with new key
	newCC, err := crypto.NewCryptoCore(newMasterKey, 1)
	if err != nil {
		t.Fatalf("Cannot create new crypto core: %v", err)
	}

	newDecrypted, err := newCC.DecryptFileEnvelope(rotatedCiphertext)
	if err != nil {
		t.Fatalf("Decryption with new key failed: %v", err)
	}

	if !bytes.Equal(plaintext, newDecrypted) {
		t.Fatal("Decrypted data with new key doesn't match original")
	}

	t.Logf("✅ Key rotation test passed")
	t.Logf("   Original size: %d bytes", len(plaintext))
	t.Logf("   File size: %d bytes (unchanged)", len(rotatedCiphertext))
	t.Logf("   Master key version: %d -> %d", oldVersion, newVersion)
}

func TestKeyRotation_Performance(t *testing.T) {
	oldMasterKey := make([]byte, 32)
	newMasterKey := make([]byte, 32)
	rand.Read(oldMasterKey)
	rand.Read(newMasterKey)

	cc, _ := crypto.NewCryptoCore(oldMasterKey, 1)

	// Test with 1MB file
	largeData := make([]byte, 1024*1024)
	rand.Read(largeData)

	// Encrypt
	start := time.Now()
	ciphertext, err := cc.EncryptFileEnvelope(largeData, 1)
	if err != nil {
		t.Fatalf("Encryption failed: %v", err)
	}
	encryptTime := time.Since(start)

	// Key rotation
	start = time.Now()
	rotatedCiphertext, err := crypto.RotateFileKey(cc, ciphertext, newMasterKey, 2)
	if err != nil {
		t.Fatalf("Key rotation failed: %v", err)
	}
	rotationTime := time.Since(start)

	// Verify
	newCC, _ := crypto.NewCryptoCore(newMasterKey, 1)
	start = time.Now()
	decrypted, err := newCC.DecryptFileEnvelope(rotatedCiphertext)
	if err != nil {
		t.Fatalf("Decryption failed: %v", err)
	}
	decryptTime := time.Since(start)

	if len(decrypted) != len(largeData) {
		t.Fatal("Decrypted data size mismatch")
	}

	t.Logf("✅ Key rotation performance test passed")
	t.Logf("   File size: %d bytes (1MB)", len(largeData))
	t.Logf("   Encrypt time: %v", encryptTime)
	t.Logf("   Rotation time: %v", rotationTime)
	t.Logf("   Decrypt time: %v", decryptTime)
	t.Logf("   Rotation speedup vs re-encryption: %.0fx", float64(encryptTime+decryptTime)/float64(rotationTime))
}

// func TestKeyRotation_FileOperations(t *testing.T) {
// 	tempDir, err := os.MkdirTemp("", "gocryptfs_rotation_test")
// 	if err != nil {
// 		t.Fatalf("Cannot create temp dir: %v", err)
// 	}
// 	defer os.RemoveAll(tempDir)

// 	oldMasterKey := make([]byte, 32)
// 	newMasterKey := make([]byte, 32)
// 	rand.Read(oldMasterKey)
// 	rand.Read(newMasterKey)

// 	cc, _ := crypto.NewCryptoCore(oldMasterKey)

// 	// Create test files
// 	testFiles := []string{"file1.txt", "file2.txt", "file3.txt"}
// 	testData := [][]byte{
// 		[]byte("Content of file 1"),
// 		[]byte("Content of file 2 - longer content"),
// 		[]byte("File 3 content"),
// 	}

// 	// Encrypt and write files
// 	for i, filename := range testFiles {
// 		ciphertext, err := cc.EncryptFileEnvelope(testData[i], 1)
// 		if err != nil {
// 			t.Fatalf("Encryption failed for %s: %v", filename, err)
// 		}

// 		filePath := filepath.Join(tempDir, filename)
// 		err = os.WriteFile(filePath, ciphertext, 0644)
// 		if err != nil {
// 			t.Fatalf("Cannot write file %s: %v", filename, err)
// 		}
// 	}

// 	// Rotate keys for all files
// 	err = cc.RotateDirectoryKeys(tempDir, newMasterKey, 2)
// 	if err != nil {
// 		t.Fatalf("Directory key rotation failed: %v", err)
// 	}

// 	// Verify all files rotated
// 	newCC, _ := crypto.NewCryptoCore(newMasterKey)
// 	for i, filename := range testFiles {
// 		filePath := filepath.Join(tempDir, filename)

// 		// Check key version
// 		version, err := GetFileKeyVersion(filePath)
// 		if err != nil {
// 			t.Fatalf("Cannot get key version for %s: %v", filename, err)
// 		}
// 		if version != 2 {
// 			t.Fatalf("Key version not updated for %s: expected 2, got %d", filename, version)
// 		}

// 		// Verify content
// 		ciphertext, err := os.ReadFile(filePath)
// 		if err != nil {
// 			t.Fatalf("Cannot read rotated file %s: %v", filename, err)
// 		}

// 		decrypted, err := newCC.DecryptFileEnvelope(ciphertext)
// 		if err != nil {
// 			t.Fatalf("Cannot decrypt rotated file %s: %v", filename, err)
// 		}

// 		if !bytes.Equal(decrypted, testData[i]) {
// 			t.Fatalf("Content mismatch for %s", filename)
// 		}

// 		// Check backup file exists
// 		backupPattern := filePath + ".backup.*"
// 		matches, err := filepath.Glob(backupPattern)
// 		if err != nil || len(matches) == 0 {
// 			t.Fatalf("Backup file not created for %s", filename)
// 		}
// 	}

// 	t.Logf("✅ File operations test passed")
// 	t.Logf("   Rotated %d files successfully", len(testFiles))
// 	t.Logf("   All backup files created")
// }
