{"name": "ui-backend", "version": "1.0.0", "description": "Backend API for WebDAV Controller", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": ["webdav", "controller", "api", "express"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5"}, "devDependencies": {"nodemon": "^3.0.1"}}