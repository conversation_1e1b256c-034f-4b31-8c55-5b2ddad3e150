import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [filesystemStatus, setFilesystemStatus] = useState('unknown');
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [filesystemInfo, setFilesystemInfo] = useState({});

  // Check filesystem status on component mount
  useEffect(() => {
    checkFilesystemStatus();
    // Auto-refresh status every 5 seconds
    const interval = setInterval(checkFilesystemStatus, 5000);
    return () => clearInterval(interval);
  }, []);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
  };

  const clearMessages = () => {
    setError('');
    setSuccess('');
  };

  const checkFilesystemStatus = async () => {
    try {
      const response = await fetch('/api/status');
      const data = await response.json();
      setFilesystemStatus(data.status);
      setFilesystemInfo(data);
      
      if (data.status !== 'unknown') {
        addLog(`Filesystem status: ${data.status}`, 'info');
      }
    } catch (err) {
      setFilesystemStatus('unknown');
      addLog('Failed to check filesystem status', 'error');
    }
  };

  const initializeFilesystem = async () => {
    if (!password) {
      setError('Password is required');
      return;
    }

    try {
      setIsLoading(true);
      clearMessages();
      addLog('Initializing encrypted filesystem...', 'info');

      const response = await fetch('/api/init', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (response.ok) {
        setSuccess('Filesystem initialized successfully!');
        addLog('Encrypted filesystem initialized', 'success');
        setPassword(''); // Clear password after successful init
        checkFilesystemStatus();
      } else {
        throw new Error(data.error || 'Failed to initialize filesystem');
      }
    } catch (err) {
      setError(err.message);
      addLog(`Initialization error: ${err.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const mountFilesystem = async () => {
    if (!password) {
      setError('Password is required');
      return;
    }

    try {
      setIsLoading(true);
      clearMessages();
      addLog('Mounting encrypted filesystem...', 'info');

      const response = await fetch('/api/mount', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (response.ok) {
        setFilesystemStatus('mounted');
        setSuccess('Filesystem mounted successfully!');
        addLog('Encrypted filesystem mounted', 'success');
        addLog('Files are now accessible in plain directory', 'success');
        setPassword(''); // Clear password after successful mount
      } else {
        throw new Error(data.error || 'Failed to mount filesystem');
      }
    } catch (err) {
      setError(err.message);
      addLog(`Mount error: ${err.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const unmountFilesystem = async () => {
    try {
      setIsLoading(true);
      clearMessages();
      addLog('Unmounting encrypted filesystem...', 'info');

      const response = await fetch('/api/unmount', { method: 'POST' });
      const data = await response.json();

      if (response.ok) {
        setFilesystemStatus('stopped');
        setSuccess('Filesystem unmounted successfully!');
        addLog('Encrypted filesystem unmounted', 'success');
      } else {
        throw new Error(data.error || 'Failed to unmount filesystem');
      }
    } catch (err) {
      setError(err.message);
      addLog(`Unmount error: ${err.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusColor = () => {
    switch (filesystemStatus) {
      case 'mounted': return '#28a745';
      case 'running': return '#ffc107';
      case 'stopped': return '#6c757d';
      default: return '#dc3545';
    }
  };

  const getStatusText = () => {
    switch (filesystemStatus) {
      case 'mounted': return 'Mounted & Ready';
      case 'running': return 'Running';
      case 'stopped': return 'Stopped';
      default: return 'Unknown';
    }
  };

  const isInitialized = filesystemInfo.configExists;
  const canMount = isInitialized && (filesystemStatus === 'stopped');
  const canUnmount = filesystemStatus === 'mounted' || filesystemStatus === 'running';

  return (
    <div className="App">
      <header className="App-header">
        <h1>🔐 gocryptfs Controller</h1>
        
        <div className="status-section">
          <div className="status-indicator">
            <span
              className="status-dot"
              style={{ backgroundColor: getStatusColor() }}
            ></span>
            <span className="status-text">Status: {getStatusText()}</span>
          </div>
          <button
            onClick={checkFilesystemStatus}
            disabled={isLoading}
            className="refresh-btn"
          >
            {isLoading ? '⟳' : '↻'} Refresh
          </button>
        </div>

        {!isInitialized && (
          <div className="password-section">
            <h3>Initialize Encrypted Filesystem</h3>
            <p>Create a new encrypted filesystem with a password.</p>
            <input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
              className="password-input"
              onKeyPress={(e) => e.key === 'Enter' && initializeFilesystem()}
            />
            <br />
            <label>
              <input
                type="checkbox"
                checked={showPassword}
                onChange={(e) => setShowPassword(e.target.checked)}
              />
              {' '}Show password
            </label>
          </div>
        )}

        {isInitialized && canMount && (
          <div className="password-section">
            <h3>Mount Encrypted Filesystem</h3>
            <p>Enter your password to mount the encrypted filesystem.</p>
            <input
              type={showPassword ? 'text' : 'password'}
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter password"
              className="password-input"
              onKeyPress={(e) => e.key === 'Enter' && mountFilesystem()}
            />
            <br />
            <label>
              <input
                type="checkbox"
                checked={showPassword}
                onChange={(e) => setShowPassword(e.target.checked)}
              />
              {' '}Show password
            </label>
          </div>
        )}

        <div className="control-section">
          {!isInitialized && (
            <button
              onClick={initializeFilesystem}
              disabled={isLoading || !password}
              className="init-btn"
            >
              {isLoading ? 'Initializing...' : '🔧 Initialize Filesystem'}
            </button>
          )}
          
          {canMount && (
            <button
              onClick={mountFilesystem}
              disabled={isLoading || !password}
              className="mount-btn"
            >
              {isLoading ? 'Mounting...' : '📁 Mount Filesystem'}
            </button>
          )}
          
          {canUnmount && (
            <button
              onClick={unmountFilesystem}
              disabled={isLoading}
              className="unmount-btn"
            >
              {isLoading ? 'Unmounting...' : '📤 Unmount Filesystem'}
            </button>
          )}
        </div>

        {error && (
          <div className="error-message">
            ⚠️ {error}
          </div>
        )}

        {success && (
          <div className="success-message">
            ✅ {success}
          </div>
        )}

        <div className="info-section">
          <h3>Filesystem Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>Status:</strong> {getStatusText()}
            </div>
            <div className="info-item">
              <strong>Initialized:</strong> {isInitialized ? 'Yes' : 'No'}
            </div>
            <div className="info-item">
              <strong>Cipher Directory:</strong> {filesystemInfo.cipherDir || 'N/A'}
            </div>
            <div className="info-item">
              <strong>Plain Directory:</strong> {filesystemInfo.plainDir || 'N/A'}
            </div>
            <div className="info-item">
              <strong>Binary Available:</strong> {filesystemInfo.binaryExists ? 'Yes' : 'No'}
            </div>
            <div className="info-item">
              <strong>Config File:</strong> {filesystemInfo.configExists ? 'Exists' : 'Not found'}
            </div>
          </div>
        </div>

        <div className="logs-section">
          <div className="logs-header">
            <h3>Activity Logs</h3>
            <button onClick={clearLogs} className="clear-logs-btn">
              Clear Logs
            </button>
          </div>
          <div className="logs-container">
            {logs.length === 0 ? (
              <div className="no-logs">No activity logs yet</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className={`log-entry log-${log.type}`}>
                  <span className="log-timestamp">[{log.timestamp}]</span>
                  <span className="log-message">{log.message}</span>
                </div>
              ))
            )}
          </div>
        </div>
      </header>
    </div>
  );
}

export default App;
