package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"os"
	"path/filepath"
	"strings"
)

const (
	// CONFIG_FILENAME là tên file config
	CONFIG_FILENAME = "gocryptfs.conf"
	// DIR_IV_FILENAME là tên file chứa DirIV
	DIR_IV_FILENAME = "gocryptfs.diriv"

	// BLOCK_SIZE là kích thước block cho mã hóa (4KB)
	BLOCK_SIZE = 4096
	// NONCE_SIZE là kích thước nonce cho AES-GCM (12 bytes)
	NONCE_SIZE = 12
	// TAG_SIZE là kích thước authentication tag cho AES-GCM (16 bytes)
	TAG_SIZE = 16
	// FILE_HEADER_SIZE là kích thước header của file (18 bytes: 2 bytes version + 16 bytes file ID)
	FILE_HEADER_SIZE = 18
	// FILE_ID_SIZE là kích thước file ID (16 bytes)
	FILE_ID_SIZE = 16
	// DIR_IV_SIZE là kích thước DirIV (16 bytes)
	DIR_IV_SIZE = 16
)

func IsContainFilename(filename string) bool {
	var SKIP_FILENAMES = []string{
		CONFIG_FILENAME,
		".",
		"..",
		".DS_Store",
	}
	for _, skip := range SKIP_FILENAMES {
		if filename == skip {
			return true
		}
	}

	if strings.HasPrefix(filename, "_backup.") || strings.HasPrefix(filename, DIR_IV_FILENAME) {
		return true
	}

	return false
}

// CryptoCore chứa các thông tin cần thiết cho DEK-based encryption
type CryptoCore struct {
	// AESGCMCipher là cipher AES-GCM cho master key operations
	AESGCMCipher cipher.AEAD
	// MasterKey là master key
	MasterKey        []byte
	MasterKeyVersion uint16
}

// NewCryptoCore tạo một CryptoCore mới từ master key
func NewCryptoCore(masterKey []byte, masterKeyVersion uint16) (*CryptoCore, error) {
	if len(masterKey) != 32 {
		return nil, fmt.Errorf("invalid master key length: %d", len(masterKey))
	}

	// Tạo AES cipher
	block, err := aes.NewCipher(masterKey)
	if err != nil {
		return nil, fmt.Errorf("cannot create AES cipher: %v", err)
	}

	// Tạo GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("cannot create GCM: %v", err)
	}

	return &CryptoCore{
		AESGCMCipher:     gcm,
		MasterKey:        masterKey,
		MasterKeyVersion: masterKeyVersion,
	}, nil
}

// -------------------- Nonce ------------------------

// createNonce tạo nonce từ file ID và block number
func (cc *CryptoCore) createNonce(fileID []byte, blockNo uint64) []byte {
	nonce := make([]byte, NONCE_SIZE)

	// Sử dụng file ID làm phần đầu của nonce (8 bytes đầu)
	copy(nonce[:8], fileID[:8])

	// Block number làm phần cuối (4 bytes cuối, little endian)
	nonce[8] = byte(blockNo)
	nonce[9] = byte(blockNo >> 8)
	nonce[10] = byte(blockNo >> 16)
	nonce[11] = byte(blockNo >> 24)

	return nonce
}

// ===================== Filename encryption (DEK-compatible) =====================

// EncryptFilename mã hóa tên file bằng AES-SIV
func (cc *CryptoCore) EncryptFilename(plainName string, cipherDir string) (string, error) {

	// Mục đích: DirIV (Directory Initialization Vector) là một chuỗi byte ngẫu nhiên, duy nhất cho mỗi thư mục. Nó đóng vai trò như một "salt" (muối) trong quy trình mã hóa.
	dirIV, err := GetDirIV(cipherDir, cc.MasterKeyVersion)
	if err != nil {
		return "", err
	}

	// Hệ thống không dùng MasterKey trực tiếp để mã hóa tên file. Thay vào đó, nó tạo ra một khóa dành riêng cho việc mã hóa tên file trong thư mục đó (filenameKey) bằng cách băm kết hợp các yếu tố sau bằng thuật toán SHA-256
	// => filenameKey

	// Tạo key cho filename encryption từ master key và dirIV
	h := sha256.New()
	h.Write(cc.MasterKey)
	h.Write(dirIV)
	h.Write([]byte("filename"))
	filenameKey := h.Sum(nil)

	// Thuật toán: AES được chọn làm thuật toán mã hóa.
	// Tạo AES cipher cho filename
	block, err := aes.NewCipher(filenameKey)
	if err != nil {
		return "", fmt.Errorf("cannot create filename cipher: %v", err)
	}

	// Vector khởi tạo (IV) cho CTR: Vector khởi tạo cho chế độ CTR được lấy trực tiếp từ dirIV của thư mục. Cụ thể, nó sẽ lấy aes.BlockSize (16 byte) đầu tiên của dirIV để làm IV.
	// Sử dụng CTR mode cho filename encryption (simplified SIV)
	iv := make([]byte, aes.BlockSize)
	copy(iv, dirIV)

	// Chế độ vận hành: CTR (Counter Mode) được sử dụng. CTR biến AES (vốn mã hóa theo từng khối 16 byte) thành một mã luồng, có thể mã hóa dữ liệu với độ dài bất kỳ mà không cần đệm (padding).
	stream := cipher.NewCTR(block, iv)

	// Mã hóa filename: XOR 2 lần là ra lại dữ kiệu cũ
	plainBytes := []byte(plainName)
	cipherBytes := make([]byte, len(plainBytes))
	stream.XORKeyStream(cipherBytes, plainBytes)

	// Encode base64 để safe cho filesystem
	encoded := base64.URLEncoding.EncodeToString(cipherBytes)

	// Loại bỏ padding: Các ký tự đệm = ở cuối chuỗi Base64 được loại bỏ để làm cho tên file ngắn gọn hơn.
	encoded = strings.TrimRight(encoded, "=")

	return encoded, nil
}

// DecryptFilename giải mã tên file
func (cc *CryptoCore) DecryptFilename(cipherName string, cipherDir string) (string, error) {

	dirIV, err := GetDirIV(cipherDir, cc.MasterKeyVersion)
	if err != nil {
		return "", err
	}

	// Khôi phục Padding và Giải mã Base64
	for len(cipherName)%4 != 0 {
		cipherName += "="
	}
	cipherBytes, err := base64.URLEncoding.DecodeString(cipherName)
	if err != nil {
		return "", fmt.Errorf("cannot decode filename: %v", err)
	}

	// Tạo key cho filename encryption từ master key và dirIV
	h := sha256.New()
	h.Write(cc.MasterKey)
	h.Write(dirIV)
	h.Write([]byte("filename"))
	filenameKey := h.Sum(nil)

	// Tạo AES cipher cho filename
	block, err := aes.NewCipher(filenameKey)
	if err != nil {
		return "", fmt.Errorf("cannot create filename cipher: %v", err)
	}

	// Sử dụng CTR mode cho filename decryption
	iv := make([]byte, aes.BlockSize)
	copy(iv, dirIV)

	stream := cipher.NewCTR(block, iv)

	// Giải mã filename: XOR 2 lần là ra lại dữ kiệu cũ
	plainBytes := make([]byte, len(cipherBytes))
	stream.XORKeyStream(plainBytes, cipherBytes)

	return string(plainBytes), nil
}

// ======================= Dir IV ====================

// Mục đích: DirIV (Directory Initialization Vector) là một chuỗi byte ngẫu nhiên, duy nhất cho mỗi thư mục. Nó đóng vai trò như một "salt" (muối) trong quy trình mã hóa.

// CreateDirIV tạo DirIV mới cho directory
func createDirIV() ([]byte, error) {
	dirIV := make([]byte, DIR_IV_SIZE)
	if _, err := rand.Read(dirIV); err != nil {
		return nil, fmt.Errorf("cannot generate DirIV: %v", err)
	}
	return dirIV, nil
}

// GetDirIV lấy DirIV cho directory, tạo mới nếu chưa có
func GetDirIV(cipherDir string, masterKeyVersion uint16) ([]byte, error) {

	fname := fmt.Sprintf("%v.%d", DIR_IV_FILENAME, masterKeyVersion)
	dirIVPath := filepath.Join(cipherDir, fname)

	// Thử đọc DirIV hiện có
	dirIV, err := os.ReadFile(dirIVPath)
	if err == nil && len(dirIV) == DIR_IV_SIZE {
		return dirIV, nil
	}

	// Tạo DirIV mới
	dirIV, err = createDirIV()
	if err != nil {
		return nil, fmt.Errorf("cannot create DirIV: %v", err)
	}

	// Ghi DirIV vào file
	if err := os.WriteFile(dirIVPath, dirIV, 0600); err != nil {
		return nil, fmt.Errorf("cannot write DirIV: %v", err)
	}

	return dirIV, nil
}
