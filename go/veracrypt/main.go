package main

import (
	"bufio"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"crypto/sha256"
	"fmt"
	"io"
	"os"
	"path/filepath"
	"strings"
	"syscall"

	"golang.org/x/crypto/pbkdf2"
	"golang.org/x/term"
)

const (
	keySize    = 32     // AES-256
	nonceSize  = 12     // GCM nonce size
	saltSize   = 32     // Salt size for PBKDF2
	iterations = 100000 // PBKDF2 iterations
)

type VeraCrypt struct {
	password string
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		return
	}

	command := os.Args[1]

	switch command {
	case "encrypt", "e":
		if len(os.Args) < 3 {
			fmt.Println("Error: Please specify input file")
			printUsage()
			return
		}
		inputFile := os.Args[2]
		outputFile := inputFile + ".encrypted"
		if len(os.Args) >= 4 {
			outputFile = os.Args[3]
		}

		password := getPassword("Enter password for encryption: ")
		if password == "" {
			fmt.Println("Error: Password cannot be empty")
			return
		}

		vc := &VeraCrypt{password: password}
		if err := vc.encryptFile(inputFile, outputFile); err != nil {
			fmt.Printf("Encryption failed: %v\n", err)
			return
		}
		fmt.Printf("File encrypted successfully: %s\n", outputFile)

	case "decrypt", "d":
		if len(os.Args) < 3 {
			fmt.Println("Error: Please specify input file")
			printUsage()
			return
		}
		inputFile := os.Args[2]
		outputFile := strings.TrimSuffix(inputFile, ".encrypted")
		if outputFile == inputFile {
			outputFile = inputFile + ".decrypted"
		}
		if len(os.Args) >= 4 {
			outputFile = os.Args[3]
		}

		password := getPassword("Enter password for decryption: ")
		if password == "" {
			fmt.Println("Error: Password cannot be empty")
			return
		}

		vc := &VeraCrypt{password: password}
		if err := vc.decryptFile(inputFile, outputFile); err != nil {
			fmt.Printf("Decryption failed: %v\n", err)
			return
		}
		fmt.Printf("File decrypted successfully: %s\n", outputFile)

	case "help", "h", "--help":
		printUsage()

	default:
		fmt.Printf("Unknown command: %s\n", command)
		printUsage()
	}
}

func printUsage() {
	fmt.Println("VeraCrypt-like File Encryption Tool")
	fmt.Println("Usage:")
	fmt.Println("  encrypt|e <input_file> [output_file]  - Encrypt a file")
	fmt.Println("  decrypt|d <input_file> [output_file]  - Decrypt a file")
	fmt.Println("  help|h                                - Show this help")
	fmt.Println()
	fmt.Println("Examples:")
	fmt.Println("  ./veracrypt encrypt document.txt")
	fmt.Println("  ./veracrypt decrypt document.txt.encrypted")
	fmt.Println("  ./veracrypt e secret.pdf encrypted_secret.pdf")
}

// getPassword prompts user for password input without echoing to terminal
func getPassword(prompt string) string {
	fmt.Print(prompt)

	// Try to get password without echo
	bytePassword, err := term.ReadPassword(int(syscall.Stdin))
	if err != nil {
		// Fallback to regular input if terminal doesn't support hidden input
		fmt.Print("(Warning: Password will be visible) ")
		reader := bufio.NewReader(os.Stdin)
		password, _ := reader.ReadString('\n')
		return strings.TrimSpace(password)
	}

	fmt.Println() // Add newline after password input
	return string(bytePassword)
}

// deriveKey derives encryption key from password using PBKDF2
func (vc *VeraCrypt) deriveKey(salt []byte) []byte {
	return pbkdf2.Key([]byte(vc.password), salt, iterations, keySize, sha256.New)
}

// encryptFile encrypts a file using AES-256-GCM
func (vc *VeraCrypt) encryptFile(inputPath, outputPath string) error {
	// Check if input file exists
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return fmt.Errorf("input file does not exist: %s", inputPath)
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Generate random salt
	salt := make([]byte, saltSize)
	if _, err := rand.Read(salt); err != nil {
		return fmt.Errorf("failed to generate salt: %v", err)
	}

	// Derive key from password
	key := vc.deriveKey(salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %v", err)
	}

	// Generate random nonce
	nonce := make([]byte, nonceSize)
	if _, err := rand.Read(nonce); err != nil {
		return fmt.Errorf("failed to generate nonce: %v", err)
	}

	// Open input file
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// Create output file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// Write salt and nonce to output file
	if _, err := outputFile.Write(salt); err != nil {
		return fmt.Errorf("failed to write salt: %v", err)
	}
	if _, err := outputFile.Write(nonce); err != nil {
		return fmt.Errorf("failed to write nonce: %v", err)
	}

	// Read and encrypt file content
	plaintext, err := io.ReadAll(inputFile)
	if err != nil {
		return fmt.Errorf("failed to read input file: %v", err)
	}

	// Encrypt the data
	ciphertext := gcm.Seal(nil, nonce, plaintext, nil)

	// Write encrypted data to output file
	if _, err := outputFile.Write(ciphertext); err != nil {
		return fmt.Errorf("failed to write encrypted data: %v", err)
	}

	return nil
}

// decryptFile decrypts a file using AES-256-GCM
func (vc *VeraCrypt) decryptFile(inputPath, outputPath string) error {
	// Check if input file exists
	if _, err := os.Stat(inputPath); os.IsNotExist(err) {
		return fmt.Errorf("input file does not exist: %s", inputPath)
	}

	// Create output directory if it doesn't exist
	if err := os.MkdirAll(filepath.Dir(outputPath), 0755); err != nil {
		return fmt.Errorf("failed to create output directory: %v", err)
	}

	// Open input file
	inputFile, err := os.Open(inputPath)
	if err != nil {
		return fmt.Errorf("failed to open input file: %v", err)
	}
	defer inputFile.Close()

	// Read salt
	salt := make([]byte, saltSize)
	if _, err := io.ReadFull(inputFile, salt); err != nil {
		return fmt.Errorf("failed to read salt: %v", err)
	}

	// Read nonce
	nonce := make([]byte, nonceSize)
	if _, err := io.ReadFull(inputFile, nonce); err != nil {
		return fmt.Errorf("failed to read nonce: %v", err)
	}

	// Derive key from password
	key := vc.deriveKey(salt)

	// Create AES cipher
	block, err := aes.NewCipher(key)
	if err != nil {
		return fmt.Errorf("failed to create cipher: %v", err)
	}

	// Create GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return fmt.Errorf("failed to create GCM: %v", err)
	}

	// Read encrypted data
	ciphertext, err := io.ReadAll(inputFile)
	if err != nil {
		return fmt.Errorf("failed to read encrypted data: %v", err)
	}

	// Decrypt the data
	plaintext, err := gcm.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return fmt.Errorf("failed to decrypt data (wrong password?): %v", err)
	}

	// Create output file
	outputFile, err := os.Create(outputPath)
	if err != nil {
		return fmt.Errorf("failed to create output file: %v", err)
	}
	defer outputFile.Close()

	// Write decrypted data to output file
	if _, err := outputFile.Write(plaintext); err != nil {
		return fmt.Errorf("failed to write decrypted data: %v", err)
	}

	return nil
}
