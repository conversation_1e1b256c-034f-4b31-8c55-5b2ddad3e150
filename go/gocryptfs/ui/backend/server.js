const express = require('express');
const { exec, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Paths
const GOCRYPTFS_BINARY = path.join(__dirname, '../../build/gocryptfs');
const CIPHER_DIR = path.join(__dirname, '../../demo/cipher');
const PLAIN_DIR = path.join(__dirname, '../../demo/plain');
const PID_FILE_PATH = path.join(__dirname, '../../gocryptfs.pid');
const CONFIG_FILE = path.join(CIPHER_DIR, 'gocryptfs.conf');

// Middleware
app.use(cors());
app.use(express.json());

// Helper function to check if process is running
function isProcessRunning(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch (e) {
    return false;
  }
}

// Helper function to check if directory is mounted
function isMounted(mountPoint) {
  try {
    // Check if mount point exists and is accessible
    if (!fs.existsSync(mountPoint)) {
      return false;
    }
    
    // On macOS/Linux, check mount table
    const { execSync } = require('child_process');
    const mountOutput = execSync('mount', { encoding: 'utf8' });
    return mountOutput.includes(mountPoint);
  } catch (error) {
    return false;
  }
}

// Helper function to get filesystem status
function getFilesystemStatus() {
  try {
    // Check if PID file exists
    if (!fs.existsSync(PID_FILE_PATH)) {
      return 'stopped';
    }
    
    const pidContent = fs.readFileSync(PID_FILE_PATH, 'utf8');
    const pid = parseInt(pidContent.trim());
    
    if (isNaN(pid)) {
      return 'stopped';
    }
    
    // Check if process is running
    if (isProcessRunning(pid)) {
      // Check if actually mounted
      if (isMounted(PLAIN_DIR)) {
        return 'mounted';
      } else {
        return 'running';
      }
    } else {
      // Clean up stale PID file
      fs.unlinkSync(PID_FILE_PATH);
      return 'stopped';
    }
  } catch (error) {
    console.error('Error checking filesystem status:', error);
    return 'unknown';
  }
}

// Helper function to ensure directories exist
function ensureDirectories() {
  try {
    if (!fs.existsSync(CIPHER_DIR)) {
      fs.mkdirSync(CIPHER_DIR, { recursive: true });
    }
    if (!fs.existsSync(PLAIN_DIR)) {
      fs.mkdirSync(PLAIN_DIR, { recursive: true });
    }
    return true;
  } catch (error) {
    console.error('Error creating directories:', error);
    return false;
  }
}

// API Routes

// Get filesystem status
app.get('/api/status', (req, res) => {
  try {
    const status = getFilesystemStatus();
    const info = {
      status,
      cipherDir: CIPHER_DIR,
      plainDir: PLAIN_DIR,
      configExists: fs.existsSync(CONFIG_FILE),
      binaryExists: fs.existsSync(GOCRYPTFS_BINARY)
    };
    res.json(info);
  } catch (error) {
    console.error('Error getting status:', error);
    res.status(500).json({ error: 'Failed to get filesystem status' });
  }
});

// Initialize encrypted filesystem
app.post('/api/init', (req, res) => {
  try {
    const { password } = req.body;
    
    if (!password) {
      return res.status(400).json({ error: 'Password is required' });
    }
    
    if (fs.existsSync(CONFIG_FILE)) {
      return res.status(400).json({ error: 'Filesystem already initialized' });
    }
    
    if (!ensureDirectories()) {
      return res.status(500).json({ error: 'Failed to create directories' });
    }
    
    // Initialize filesystem
    const initProcess = spawn(GOCRYPTFS_BINARY, ['-init', CIPHER_DIR], {
      stdio: ['pipe', 'pipe', 'pipe']
    });
    
    // Send password to stdin
    initProcess.stdin.write(password + '\n');
    initProcess.stdin.end();
    
    let stdout = '';
    let stderr = '';
    
    initProcess.stdout.on('data', (data) => {
      stdout += data.toString();
    });
    
    initProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    initProcess.on('close', (code) => {
      if (code === 0) {
        res.json({ 
          success: true, 
          message: 'Filesystem initialized successfully',
          output: stdout
        });
      } else {
        res.status(500).json({ 
          error: 'Failed to initialize filesystem',
          output: stderr || stdout
        });
      }
    });
    
    initProcess.on('error', (error) => {
      res.status(500).json({ 
        error: 'Failed to start initialization process',
        details: error.message
      });
    });
    
  } catch (error) {
    console.error('Error initializing filesystem:', error);
    res.status(500).json({ error: 'Failed to initialize filesystem' });
  }
});

// Mount encrypted filesystem
app.post('/api/mount', (req, res) => {
  try {
    const { password } = req.body;
    
    if (!password) {
      return res.status(400).json({ error: 'Password is required' });
    }
    
    const currentStatus = getFilesystemStatus();
    if (currentStatus === 'mounted' || currentStatus === 'running') {
      return res.status(400).json({ error: 'Filesystem is already mounted' });
    }
    
    if (!fs.existsSync(CONFIG_FILE)) {
      return res.status(400).json({ error: 'Filesystem not initialized. Run init first.' });
    }
    
    if (!ensureDirectories()) {
      return res.status(500).json({ error: 'Failed to create directories' });
    }
    
    // Mount filesystem
    const mountProcess = spawn(GOCRYPTFS_BINARY, [CIPHER_DIR, PLAIN_DIR], {
      stdio: ['pipe', 'pipe', 'pipe'],
      detached: true
    });
    
    // Send password to stdin
    mountProcess.stdin.write(password + '\n');
    mountProcess.stdin.end();
    
    // Save PID
    fs.writeFileSync(PID_FILE_PATH, mountProcess.pid.toString());
    
    let stderr = '';
    
    mountProcess.stderr.on('data', (data) => {
      stderr += data.toString();
    });
    
    // Wait a moment for mount to complete
    setTimeout(() => {
      const status = getFilesystemStatus();
      if (status === 'mounted') {
        res.json({ 
          success: true, 
          message: 'Filesystem mounted successfully',
          status: 'mounted',
          pid: mountProcess.pid
        });
      } else {
        res.status(500).json({ 
          error: 'Failed to mount filesystem',
          output: stderr,
          status: status
        });
      }
    }, 2000);
    
    mountProcess.on('error', (error) => {
      res.status(500).json({ 
        error: 'Failed to start mount process',
        details: error.message
      });
    });
    
  } catch (error) {
    console.error('Error mounting filesystem:', error);
    res.status(500).json({ error: 'Failed to mount filesystem' });
  }
});

// Unmount filesystem
app.post('/api/unmount', (req, res) => {
  try {
    const currentStatus = getFilesystemStatus();
    
    if (currentStatus === 'stopped') {
      return res.status(400).json({ error: 'Filesystem is already unmounted' });
    }
    
    // Try to unmount using umount command
    const umountCommand = process.platform === 'darwin' ? 
      `umount "${PLAIN_DIR}"` : 
      `fusermount -u "${PLAIN_DIR}"`;
    
    exec(umountCommand, (error, stdout, stderr) => {
      // Clean up PID file
      if (fs.existsSync(PID_FILE_PATH)) {
        fs.unlinkSync(PID_FILE_PATH);
      }
      
      if (error) {
        console.error('Unmount error:', error);
        // Try to kill process if umount failed
        if (fs.existsSync(PID_FILE_PATH)) {
          const pidContent = fs.readFileSync(PID_FILE_PATH, 'utf8');
          const pid = parseInt(pidContent.trim());
          if (!isNaN(pid)) {
            try {
              process.kill(pid, 'SIGTERM');
            } catch (killError) {
              console.error('Error killing process:', killError);
            }
          }
        }
      }
      
      res.json({ 
        success: true, 
        message: 'Filesystem unmounted successfully',
        status: 'stopped'
      });
    });
    
  } catch (error) {
    console.error('Error unmounting filesystem:', error);
    res.status(500).json({ error: 'Failed to unmount filesystem' });
  }
});

// Get filesystem info
app.get('/api/info', (req, res) => {
  try {
    const status = getFilesystemStatus();
    const info = {
      status,
      cipherDir: CIPHER_DIR,
      plainDir: PLAIN_DIR,
      configFile: CONFIG_FILE,
      binaryPath: GOCRYPTFS_BINARY,
      pidFile: PID_FILE_PATH,
      configExists: fs.existsSync(CONFIG_FILE),
      binaryExists: fs.existsSync(GOCRYPTFS_BINARY),
      directoriesExist: {
        cipher: fs.existsSync(CIPHER_DIR),
        plain: fs.existsSync(PLAIN_DIR)
      }
    };
    
    res.json(info);
  } catch (error) {
    console.error('Error getting filesystem info:', error);
    res.status(500).json({ error: 'Failed to get filesystem info' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'gocryptfs Controller API'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'API endpoint not found' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`gocryptfs Controller API server running on http://localhost:${PORT}`);
  console.log(`Binary path: ${GOCRYPTFS_BINARY}`);
  console.log(`Cipher directory: ${CIPHER_DIR}`);
  console.log(`Plain directory: ${PLAIN_DIR}`);
  
  // Check initial filesystem status
  const initialStatus = getFilesystemStatus();
  console.log(`Initial filesystem status: ${initialStatus}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down API server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nShutting down API server...');
  process.exit(0);
});
