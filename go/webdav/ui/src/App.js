import React, { useState, useEffect } from 'react';
import './App.css';

function App() {
  const [serverStatus, setServerStatus] = useState('unknown');
  const [isLoading, setIsLoading] = useState(false);
  const [logs, setLogs] = useState([]);
  const [error, setError] = useState('');

  // Check server status on component mount
  useEffect(() => {
    checkServerStatus();
  }, []);

  const addLog = (message, type = 'info') => {
    const timestamp = new Date().toLocaleTimeString();
    setLogs(prev => [...prev, { timestamp, message, type }]);
  };

  const checkServerStatus = async () => {
    try {
      setIsLoading(true);
      const response = await fetch('/api/status');
      const data = await response.json();
      setServerStatus(data.status);
      addLog(`Server status: ${data.status}`, 'info');
    } catch (err) {
      setServerStatus('unknown');
      addLog('Failed to check server status', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const startServer = async () => {
    try {
      setIsLoading(true);
      setError('');
      addLog('Starting WebDAV server...', 'info');

      const response = await fetch('/api/start', { method: 'POST' });
      const data = await response.json();

      if (response.ok) {
        setServerStatus('running');
        addLog('WebDAV server started successfully', 'success');
        addLog('Volume mounted automatically', 'success');
      } else {
        throw new Error(data.error || 'Failed to start server');
      }
    } catch (err) {
      setError(err.message);
      addLog(`Error: ${err.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const stopServer = async () => {
    try {
      setIsLoading(true);
      setError('');
      addLog('Stopping WebDAV server...', 'info');

      const response = await fetch('/api/stop', { method: 'POST' });
      const data = await response.json();

      if (response.ok) {
        setServerStatus('stopped');
        addLog('WebDAV server stopped successfully', 'success');
        addLog('Volume unmounted', 'success');
      } else {
        throw new Error(data.error || 'Failed to stop server');
      }
    } catch (err) {
      setError(err.message);
      addLog(`Error: ${err.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const clearLogs = () => {
    setLogs([]);
  };

  const getStatusColor = () => {
    switch (serverStatus) {
      case 'running': return '#4CAF50';
      case 'stopped': return '#f44336';
      default: return '#ff9800';
    }
  };

  const getStatusText = () => {
    switch (serverStatus) {
      case 'running': return 'Running';
      case 'stopped': return 'Stopped';
      default: return 'Unknown';
    }
  };

  return (
    <div className="App">
      <header className="App-header">
        <h1>WebDAV Server Controller</h1>
        <div className="status-section">
          <div className="status-indicator">
            <span
              className="status-dot"
              style={{ backgroundColor: getStatusColor() }}
            ></span>
            <span className="status-text">Status: {getStatusText()}</span>
          </div>
          <button
            onClick={checkServerStatus}
            disabled={isLoading}
            className="refresh-btn"
          >
            {isLoading ? '⟳' : '↻'} Refresh
          </button>
        </div>

        <div className="control-section">
          <button
            onClick={startServer}
            disabled={isLoading || serverStatus === 'running'}
            className="start-btn"
          >
            {isLoading ? 'Starting...' : '▶ Start Server'}
          </button>
          <button
            onClick={stopServer}
            disabled={isLoading || serverStatus === 'stopped'}
            className="stop-btn"
          >
            {isLoading ? 'Stopping...' : '⏹ Stop Server'}
          </button>
        </div>

        {error && (
          <div className="error-message">
            ⚠ {error}
          </div>
        )}

        <div className="info-section">
          <h3>Server Information</h3>
          <div className="info-grid">
            <div className="info-item">
              <strong>URL:</strong> https://localhost:8443
            </div>
            <div className="info-item">
              <strong>Mount Point:</strong> /Volumes/localhost
            </div>
            <div className="info-item">
              <strong>Cipher Directory:</strong> ./cipher
            </div>
          </div>
        </div>

        <div className="logs-section">
          <div className="logs-header">
            <h3>Activity Logs</h3>
            <button onClick={clearLogs} className="clear-logs-btn">
              Clear Logs
            </button>
          </div>
          <div className="logs-container">
            {logs.length === 0 ? (
              <div className="no-logs">No activity logs yet</div>
            ) : (
              logs.map((log, index) => (
                <div key={index} className={`log-entry log-${log.type}`}>
                  <span className="log-timestamp">[{log.timestamp}]</span>
                  <span className="log-message">{log.message}</span>
                </div>
              ))
            )}
          </div>
        </div>
      </header>
    </div>
  );
}

export default App;
