# gocryptfs UI Controller

> 📖 **Main Documentation**: Xem [../README.md](../README.md) để biết đầy đủ thông tin về gocryptfs DEK-only

Web-based user interface để quản lý gocryptfs DEK-only encrypted filesystem. UI này cung cấp giao diện đồ họa để initialize, mount, và unmount encrypted filesystems một cách dễ dàng.

## ✨ Tính năng

- 🔧 **Initialize Filesystem**: Tạo encrypted filesystem mới với password
- 📁 **Mount/Unmount**: Mount và unmount filesystem với giao diện đơn giản
- 📊 **Real-time Status**: Theo dõi trạng thái filesystem real-time
- 📝 **Activity Logs**: Xem logs của tất cả operations
- 🔐 **Password Management**: Secure password input với show/hide option
- 📱 **Responsive Design**: Hoạt động tốt trên desktop và mobile

## 🏗️ Kiến trúc

```
ui/
├── backend/                # Node.js Express API server
│   ├── server.js          # Main backend server
│   └── package.json       # Backend dependencies
├── src/                   # React frontend source
│   ├── App.js            # Main React component
│   ├── App.css           # Styling
│   ├── index.js          # React entry point
│   └── index.css         # Global styles
├── public/               # Static assets
│   ├── index.html        # HTML template
│   └── manifest.json     # PWA manifest
├── package.json          # Frontend dependencies
├── start-ui.sh          # UI startup script
└── README.md            # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js 16+ và npm
- gocryptfs binary (sẽ được build tự động)
- FUSE support (macOS: macFUSE, Linux: fuse)

### Chạy UI

```bash
# Từ thư mục gocryptfs root
cd ui

# Start cả backend và frontend
./start-ui.sh
```

Script sẽ:
1. Check prerequisites
2. Build gocryptfs binary nếu cần
3. Install dependencies
4. Start backend API server (port 3001)
5. Start React frontend (port 3000)
6. Mở browser tự động

### Manual Setup

Nếu muốn chạy manual:

```bash
# Install dependencies
cd ui
npm install
cd backend
npm install
cd ..

# Start backend
cd backend
npm start &

# Start frontend (trong terminal khác)
cd ui
npm start
```

## 🖥️ Sử dụng UI

### 1. Initialize Filesystem

Lần đầu sử dụng:
1. Mở http://localhost:3000
2. Nhập password mạnh
3. Click "Initialize Filesystem"
4. Đợi quá trình hoàn thành

### 2. Mount Filesystem

Sau khi đã initialize:
1. Nhập password
2. Click "Mount Filesystem"
3. Filesystem sẽ được mount tại `demo/plain/`
4. Files có thể được access thông qua plain directory

### 3. Unmount Filesystem

Khi hoàn thành:
1. Click "Unmount Filesystem"
2. Files sẽ không còn accessible
3. Encrypted data vẫn an toàn trong cipher directory

## 🔧 API Endpoints

Backend cung cấp REST API:

### Status
- `GET /api/status` - Get filesystem status
- `GET /api/info` - Get detailed filesystem info
- `GET /api/health` - Health check

### Operations
- `POST /api/init` - Initialize filesystem
  ```json
  { "password": "your_password" }
  ```
- `POST /api/mount` - Mount filesystem
  ```json
  { "password": "your_password" }
  ```
- `POST /api/unmount` - Unmount filesystem

## 📊 Status States

- **Unknown**: Không thể xác định trạng thái
- **Stopped**: Filesystem chưa được mount
- **Running**: Process đang chạy nhưng chưa mount
- **Mounted**: Filesystem đã mount và sẵn sàng sử dụng

## 🎨 UI Components

### Status Indicator
- Colored dot hiển thị trạng thái hiện tại
- Auto-refresh mỗi 5 giây
- Manual refresh button

### Password Section
- Secure password input
- Show/hide password toggle
- Enter key support

### Control Buttons
- Initialize: Tạo filesystem mới
- Mount: Mount existing filesystem
- Unmount: Unmount filesystem

### Information Panel
- Filesystem status
- Directory paths
- Binary availability
- Config file status

### Activity Logs
- Real-time operation logs
- Timestamp cho mỗi action
- Color-coded log levels (info, success, error)
- Clear logs functionality

## 🔒 Security

- Passwords không được store trong browser
- API chỉ accept localhost connections
- HTTPS có thể được enable cho production
- Filesystem operations require valid password

## 🛠️ Development

### Frontend Development

```bash
cd ui
npm start
```

React app sẽ chạy ở development mode với hot reload.

### Backend Development

```bash
cd ui/backend
npm run dev  # Sử dụng nodemon cho auto-restart
```

### Build for Production

```bash
cd ui
npm run build
```

Tạo optimized production build trong `build/` folder.

## 🐛 Troubleshooting

### Port Already in Use
```bash
# Kill processes on ports
lsof -ti :3000 | xargs kill -9
lsof -ti :3001 | xargs kill -9
```

### FUSE Issues
```bash
# macOS: Install macFUSE
brew install --cask macfuse

# Linux: Install fuse
sudo apt-get install fuse
```

### Permission Issues
```bash
# Ensure user can use FUSE
sudo usermod -a -G fuse $USER
```

### Binary Not Found
```bash
# Build gocryptfs binary
cd ..
make build
```

## 📝 Configuration

### Environment Variables

Backend có thể được configure với environment variables:

- `PORT`: Backend port (default: 3001)
- `GOCRYPTFS_BINARY`: Path to gocryptfs binary
- `CIPHER_DIR`: Cipher directory path
- `PLAIN_DIR`: Plain directory path

### Customization

- Modify `src/App.css` để thay đổi styling
- Update `backend/server.js` để thêm API endpoints
- Customize paths trong backend configuration

## 🤝 Integration

UI có thể được integrate với:
- System tray applications
- Desktop shortcuts
- Startup scripts
- CI/CD pipelines

## 📄 License

Same license as gocryptfs project (MIT).

---

**💡 Tip**: Sử dụng strong passwords và backup cipher directory thường xuyên!
