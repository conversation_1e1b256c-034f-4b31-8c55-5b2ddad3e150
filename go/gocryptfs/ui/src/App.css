.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.App-header {
  background-color: rgba(255, 255, 255, 0.95);
  padding: 40px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
  max-width: 800px;
  margin: 0 auto;
  backdrop-filter: blur(10px);
}

.App-header h1 {
  color: #333;
  margin-bottom: 30px;
  font-size: 2.5rem;
  font-weight: 700;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.status-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
}

.status-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #333;
}

.refresh-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background-color: #5a6268;
  transform: translateY(-2px);
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.control-section {
  display: flex;
  gap: 20px;
  justify-content: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
}

.init-btn, .mount-btn, .unmount-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 140px;
}

.init-btn {
  background-color: #17a2b8;
  color: white;
}

.init-btn:hover {
  background-color: #138496;
  transform: translateY(-2px);
}

.mount-btn {
  background-color: #28a745;
  color: white;
}

.mount-btn:hover {
  background-color: #218838;
  transform: translateY(-2px);
}

.unmount-btn {
  background-color: #dc3545;
  color: white;
}

.unmount-btn:hover {
  background-color: #c82333;
  transform: translateY(-2px);
}

.init-btn:disabled, .mount-btn:disabled, .unmount-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.password-section {
  margin-bottom: 30px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 12px;
  border: 2px solid #e9ecef;
}

.password-section h3 {
  margin-bottom: 15px;
  color: #333;
}

.password-input {
  width: 100%;
  max-width: 300px;
  padding: 10px;
  border: 2px solid #ced4da;
  border-radius: 8px;
  font-size: 1rem;
  margin-bottom: 10px;
}

.password-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.error-message {
  background-color: #f8d7da;
  color: #721c24;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #f5c6cb;
}

.success-message {
  background-color: #d4edda;
  color: #155724;
  padding: 12px;
  border-radius: 8px;
  margin-bottom: 20px;
  border: 1px solid #c3e6cb;
}

.info-section {
  margin-bottom: 30px;
  text-align: left;
}

.info-section h3 {
  color: #333;
  margin-bottom: 15px;
  text-align: center;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
}

.info-item {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  border: 1px solid #e9ecef;
}

.info-item strong {
  color: #495057;
}

.logs-section {
  text-align: left;
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-header h3 {
  color: #333;
  margin: 0;
}

.clear-logs-btn {
  background-color: #6c757d;
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 0.8rem;
  transition: all 0.3s ease;
}

.clear-logs-btn:hover {
  background-color: #5a6268;
}

.logs-container {
  background-color: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 15px;
  max-height: 300px;
  overflow-y: auto;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.no-logs {
  color: #6c757d;
  font-style: italic;
  text-align: center;
  padding: 20px;
}

.log-entry {
  margin-bottom: 8px;
  padding: 8px;
  border-radius: 4px;
  border-left: 4px solid #ccc;
}

.log-info {
  background-color: #e7f3ff;
  border-left-color: #007bff;
}

.log-success {
  background-color: #e8f5e8;
  border-left-color: #28a745;
}

.log-error {
  background-color: #ffe6e6;
  border-left-color: #dc3545;
}

.log-warning {
  background-color: #fff3cd;
  border-left-color: #ffc107;
}

.log-timestamp {
  color: #6c757d;
  margin-right: 10px;
  font-weight: 600;
}

.log-message {
  color: #333;
}

@media (max-width: 768px) {
  .App-header {
    padding: 20px;
    margin: 10px;
  }
  
  .App-header h1 {
    font-size: 2rem;
  }
  
  .status-section {
    flex-direction: column;
    gap: 15px;
  }
  
  .control-section {
    flex-direction: column;
    align-items: center;
  }
  
  .init-btn, .mount-btn, .unmount-btn {
    width: 100%;
    max-width: 200px;
  }
  
  .info-grid {
    grid-template-columns: 1fr;
  }
}
