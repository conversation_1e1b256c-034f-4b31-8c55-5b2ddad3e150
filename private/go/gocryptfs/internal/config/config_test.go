package config

import (
	"encoding/json"
	"os"
	"path/filepath"
	"testing"
)

// TestNewConfig_DEKOnly kiểm tra tạo config DEK-only
func TestNewConfig_DEKOnly(t *testing.T) {
	cfg := NewConfig()

	// if cfg.Creator != "gocryptfs-dek v2.0.0" {
	// 	t.<PERSON><PERSON><PERSON>("Expected creator 'gocryptfs-dek v2.0.0', got '%s'", cfg.Creator)
	// }

	if cfg.Version != 3 {
		t.<PERSON><PERSON><PERSON>("Expected version 3 (DEK-only), got %d", cfg.Version)
	}

	// expectedFlags := []string{"DirIV", "LongNames", "EnvelopeEncryption"}
	// if len(cfg.FeatureFlags) != len(expectedFlags) {
	// 	t.Errorf("Expected %d feature flags, got %d", len(expectedFlags), len(cfg.FeatureFlags))
	// }

	// for i, flag := range expectedFlags {
	// 	if cfg.FeatureFlags[i] != flag {
	// 		t.<PERSON><PERSON><PERSON>("Expected feature flag '%s', got '%s'", flag, cfg.FeatureFlags[i])
	// 	}
	// }

	// // Verify envelope encryption is enabled
	// flags := cfg.GetFeatureFlags()
	// if !flags.EnvelopeEncryption {
	// 	t.Error("EnvelopeEncryption should be enabled for DEK-only mode")
	// }
}

// TestConfig_WriteAndLoad_DEKOnly kiểm tra ghi và đọc config DEK-only
func TestConfig_WriteAndLoad_DEKOnly(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "gocryptfs_dek_test")
	if err != nil {
		t.Fatalf("Cannot create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "gocryptfs.conf")
	password := "test_password_dek_123"

	// Create and write config
	cfg := NewConfig()
	err = cfg.WriteConfig(configPath, password)
	if err != nil {
		t.Fatalf("Cannot write config: %v", err)
	}

	// Check file exists
	if _, err := os.Stat(configPath); os.IsNotExist(err) {
		t.Fatal("Config file was not created")
	}

	// Load config
	loadedCfg, err := LoadConfig(configPath)
	if err != nil {
		t.Fatalf("Cannot load config: %v", err)
	}

	// // Check fields
	// if loadedCfg.Creator != cfg.Creator {
	// 	t.Errorf("Creator mismatch: expected '%s', got '%s'", cfg.Creator, loadedCfg.Creator)
	// }

	if loadedCfg.Version != cfg.Version {
		t.Errorf("Version mismatch: expected %d, got %d", cfg.Version, loadedCfg.Version)
	}

	if len(loadedCfg.EncryptedKey) != MASTER_KEY_LEN {
		t.Errorf("Expected encrypted key length %d, got %d", MASTER_KEY_LEN, len(loadedCfg.EncryptedKey))
	}

	if len(loadedCfg.ScryptObject.Salt) != SALT_LEN {
		t.Errorf("Expected salt length %d, got %d", SALT_LEN, len(loadedCfg.ScryptObject.Salt))
	}

	// // Verify envelope encryption is enabled
	// if !loadedCfg.IsEnvelopeEncryptionEnabled() {
	// 	t.Error("Loaded config should have envelope encryption enabled")
	// }
}

// TestConfig_DecryptMasterKey_DEKOnly kiểm tra giải mã master key
func TestConfig_DecryptMasterKey_DEKOnly(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "gocryptfs_dek_test")
	if err != nil {
		t.Fatalf("Cannot create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "gocryptfs.conf")
	password := "correct_password_dek"

	// Create config
	cfg := NewConfig()
	err = cfg.WriteConfig(configPath, password)
	if err != nil {
		t.Fatalf("Cannot write config: %v", err)
	}

	// Load config
	loadedCfg, err := LoadConfig(configPath)
	if err != nil {
		t.Fatalf("Cannot load config: %v", err)
	}

	// Decrypt with correct password
	masterKey, err := loadedCfg.DecryptMasterKey(password)
	if err != nil {
		t.Fatalf("Cannot decrypt master key with correct password: %v", err)
	}

	if len(masterKey) != MASTER_KEY_LEN {
		t.Errorf("Expected master key length %d, got %d", MASTER_KEY_LEN, len(masterKey))
	}

	// Try with wrong password
	wrongPassword := "wrong_password_dek"
	_, err = loadedCfg.DecryptMasterKey(wrongPassword)
	if err != nil {
		t.Logf("Decryption with wrong password returned error (expected): %v", err)
	}
}

// TestConfig_ValidateConfig_DEKOnly kiểm tra validation cho DEK-only
func TestConfig_ValidateConfig_DEKOnly(t *testing.T) {
	tests := []struct {
		name      string
		setupFunc func() *Config
		wantError bool
	}{
		{
			name: "Valid DEK-only config",
			setupFunc: func() *Config {
				cfg := NewConfig()
				cfg.EncryptedKey = make([]byte, MASTER_KEY_LEN)
				cfg.ScryptObject.Salt = make([]byte, SALT_LEN)
				cfg.ScryptObject.KeyLen = SCRYPT_KEY_LEN
				return cfg
			},
			wantError: false,
		},
		{
			name: "Invalid version (not 3)",
			setupFunc: func() *Config {
				cfg := NewConfig()
				cfg.Version = 2 // Legacy version
				cfg.EncryptedKey = make([]byte, MASTER_KEY_LEN)
				cfg.ScryptObject.Salt = make([]byte, SALT_LEN)
				cfg.ScryptObject.KeyLen = SCRYPT_KEY_LEN
				return cfg
			},
			wantError: true,
		},
		{
			name: "Missing envelope encryption flag",
			setupFunc: func() *Config {
				cfg := NewConfig()
				cfg.FeatureFlags = []string{"DirIV", "LongNames"} // Missing EnvelopeEncryption
				cfg.EncryptedKey = make([]byte, MASTER_KEY_LEN)
				cfg.ScryptObject.Salt = make([]byte, SALT_LEN)
				cfg.ScryptObject.KeyLen = SCRYPT_KEY_LEN
				return cfg
			},
			wantError: false, // GetFeatureFlags() auto-enables EnvelopeEncryption
		},
		{
			name: "Invalid encrypted key length",
			setupFunc: func() *Config {
				cfg := NewConfig()
				cfg.EncryptedKey = make([]byte, MASTER_KEY_LEN-1)
				cfg.ScryptObject.Salt = make([]byte, SALT_LEN)
				cfg.ScryptObject.KeyLen = SCRYPT_KEY_LEN
				return cfg
			},
			wantError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			cfg := tt.setupFunc()
			err := cfg.ValidateConfig()
			if tt.wantError && err == nil {
				t.Error("Expected validation error, got nil")
			}
			if !tt.wantError && err != nil {
				t.Errorf("Unexpected validation error: %v", err)
			}
		})
	}
}

// TestConfig_MasterKeyVersion kiểm tra master key version functions
func TestConfig_MasterKeyVersion(t *testing.T) {
	cfg := NewConfig()

	// Test default version
	if cfg.MasterKeyVersion != 1 {
		t.Errorf("Expected default master key version 1, got %d", cfg.MasterKeyVersion)
	}

	// Test set version
	cfg.SetMasterKeyVersion(5)
	if cfg.MasterKeyVersion != 5 {
		t.Errorf("Expected master key version 5, got %d", cfg.MasterKeyVersion)
	}
}

// TestConfig_JSONSerialization_DEKOnly kiểm tra JSON serialization cho DEK-only
func TestConfig_JSONSerialization_DEKOnly(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "gocryptfs_dek_test")
	if err != nil {
		t.Fatalf("Cannot create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "gocryptfs.conf")
	password := "test_password_dek"

	// Create config
	cfg := NewConfig()
	err = cfg.WriteConfig(configPath, password)
	if err != nil {
		t.Fatalf("Cannot write config: %v", err)
	}

	// Read JSON file directly
	jsonData, err := os.ReadFile(configPath)
	if err != nil {
		t.Fatalf("Cannot read config file: %v", err)
	}

	// Parse JSON
	var parsedConfig map[string]interface{}
	err = json.Unmarshal(jsonData, &parsedConfig)
	if err != nil {
		t.Fatalf("Cannot parse JSON: %v", err)
	}

	// // Check fields
	// if parsedConfig["Creator"] != cfg.Creator {
	// 	t.Errorf("Creator mismatch in JSON")
	// }

	if parsedConfig["Version"].(float64) != float64(cfg.Version) {
		t.Errorf("Version mismatch in JSON")
	}

	// Check FeatureFlags
	flags, ok := parsedConfig["FeatureFlags"].([]interface{})
	if !ok {
		t.Fatal("FeatureFlags not found or wrong type in JSON")
	}

	if len(flags) != len(cfg.FeatureFlags) {
		t.Errorf("FeatureFlags length mismatch in JSON")
	}

	// Verify EnvelopeEncryption flag is present
	hasEnvelopeEncryption := false
	for _, flag := range flags {
		if flag.(string) == "EnvelopeEncryption" {
			hasEnvelopeEncryption = true
			break
		}
	}
	if !hasEnvelopeEncryption {
		t.Error("EnvelopeEncryption flag should be present in JSON")
	}
}

// TestConfig_LoadNonExistentFile kiểm tra load file không tồn tại
func TestConfig_LoadNonExistentFile(t *testing.T) {
	_, err := LoadConfig("/non/existent/path/gocryptfs.conf")
	if err == nil {
		t.Error("Expected error when loading non-existent file")
	}
}

// TestConfig_LoadInvalidJSON kiểm tra load invalid JSON
func TestConfig_LoadInvalidJSON(t *testing.T) {
	tempDir, err := os.MkdirTemp("", "gocryptfs_dek_test")
	if err != nil {
		t.Fatalf("Cannot create temp dir: %v", err)
	}
	defer os.RemoveAll(tempDir)

	configPath := filepath.Join(tempDir, "invalid.conf")

	// Write invalid JSON
	err = os.WriteFile(configPath, []byte("invalid json content"), 0600)
	if err != nil {
		t.Fatalf("Cannot write invalid config: %v", err)
	}

	_, err = LoadConfig(configPath)
	if err == nil {
		t.Error("Expected error when loading invalid JSON")
	}
}

// TestCreateMasterKeyChecksum kiểm tra checksum creation
func TestCreateMasterKeyChecksum(t *testing.T) {
	masterKey := make([]byte, 32)
	for i := range masterKey {
		masterKey[i] = byte(i)
	}

	checksum1 := CreateMasterKeyChecksum(masterKey)
	checksum2 := CreateMasterKeyChecksum(masterKey)

	// Checksum should be deterministic
	if len(checksum1) != 16 {
		t.Errorf("Expected checksum length 16, got %d", len(checksum1))
	}

	if string(checksum1) != string(checksum2) {
		t.Error("Checksum should be deterministic")
	}

	// Different key should produce different checksum
	differentKey := make([]byte, 32)
	for i := range differentKey {
		differentKey[i] = byte(i + 1)
	}

	checksum3 := CreateMasterKeyChecksum(differentKey)
	if string(checksum1) == string(checksum3) {
		t.Error("Different keys should produce different checksums")
	}
}

// // TestGetFeatureFlags_DEKOnly kiểm tra feature flags cho DEK-only
// func TestGetFeatureFlags_DEKOnly(t *testing.T) {
// 	cfg := NewConfig()
// 	flags := cfg.GetFeatureFlags()

// 	// Check required DEK-only flags
// 	if !flags.DirIV {
// 		t.Error("DirIV should be enabled for DEK-only mode")
// 	}

// 	if !flags.LongNames {
// 		t.Error("LongNames should be enabled for DEK-only mode")
// 	}

// 	if !flags.EnvelopeEncryption {
// 		t.Error("EnvelopeEncryption should be enabled for DEK-only mode")
// 	}

// 	// Check that envelope encryption is always enabled
// 	cfg.FeatureFlags = []string{"DirIV", "LongNames"} // Remove EnvelopeEncryption
// 	flags = cfg.GetFeatureFlags()
// 	if !flags.EnvelopeEncryption {
// 		t.Error("EnvelopeEncryption should be forced to true for DEK-only mode")
// 	}
// }
