package fusefs

import (
	"fmt"
	"gocryptfs/internal/crypto"
	"os"
	"path/filepath"
	"strings"
	"sync"
	"time"
)

// MigrationService quản lý background migration
type MigrationService struct {
	CryptoCore          *crypto.CryptoCore
	NewMasterKey        []byte
	NewMasterKeyVersion uint16
	CipherDir           string

	// Control
	running    bool
	stopChan   chan bool
	pause<PERSON>han  chan bool
	resume<PERSON>han chan bool

	// Progress tracking
	TotalFiles    int
	MigratedFiles int
	FailedFiles   int
	CurrentFile   string

	// Settings
	BatchSize     int           // Files per batch
	SleepInterval time.Duration // Sleep between batches
	MaxRetries    int           // Max retries per file

	mutex sync.RWMutex
}

// NewMigrationService tạo migration service mới
func NewMigrationService(cc *crypto.CryptoCore, cipherDir string, newMasterKey []byte, newMasterKeyVersion uint16) *MigrationService {
	return &MigrationService{
		CryptoCore:          cc,
		NewMasterKey:        newMaster<PERSON><PERSON>,
		NewMasterKeyVersion: newMasterKeyVersion,
		CipherDir:           cipherDir,
		stop<PERSON>han:            make(chan bool),
		pauseChan:           make(chan bool),
		resume<PERSON>han:          make(chan bool),
		BatchSize:           10,
		SleepInterval:       100 * time.Millisecond,
		MaxRetries:          3,
	}
}

// Start bắt đầu background migration
func (ms *MigrationService) Start() error {
	ms.mutex.Lock()
	if ms.running {
		ms.mutex.Unlock()
		return fmt.Errorf("migration service already running")
	}
	ms.running = true
	ms.mutex.Unlock()

	// Tìm tất cả files cần migrate
	filesToMigrate, err := ms.findFilesToMigrate()
	if err != nil {
		return fmt.Errorf("cannot find files to migrate: %v", err)
	}

	ms.TotalFiles = len(filesToMigrate)
	if ms.TotalFiles == 0 {
		return fmt.Errorf("no files need migration")
	}

	// Start background goroutine
	go ms.runMigration(filesToMigrate)

	return nil
}

// Stop dừng migration service
func (ms *MigrationService) Stop() {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	if ms.running {
		ms.stopChan <- true
		ms.running = false
	}
}

// Pause tạm dừng migration
func (ms *MigrationService) Pause() {
	ms.pauseChan <- true
}

// Resume tiếp tục migration
func (ms *MigrationService) Resume() {
	ms.resumeChan <- true
}

// GetProgress trả về progress hiện tại
func (ms *MigrationService) GetProgress() (int, int, int, string) {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	return ms.TotalFiles, ms.MigratedFiles, ms.FailedFiles, ms.CurrentFile
}

// IsRunning kiểm tra xem service có đang chạy không
func (ms *MigrationService) IsRunning() bool {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	return ms.running
}

// findFilesToMigrate tìm tất cả files cần migrate
func (ms *MigrationService) findFilesToMigrate() ([]string, error) {
	var filesToMigrate []string

	err := filepath.Walk(ms.CipherDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories, special files, và backup files
		if info.IsDir() || crypto.IsContainFilename(info.Name()) || isBackupFile(info.Name()) {
			return nil
		}

		// Check if file needs migration
		if ms.needsMigration(path) {
			filesToMigrate = append(filesToMigrate, path)
		}

		return nil
	})

	return filesToMigrate, err
}

// needsMigration kiểm tra xem file có cần migrate không
func (ms *MigrationService) needsMigration(filePath string) bool {
	// Check if file is envelope encrypted
	if !IsEnvelopeEncryptedFile(filePath) {
		return false
	}

	// Check master key version
	version, err := GetFileKeyVersion(filePath)
	if err != nil {
		return false
	}

	// Migrate files that have older version than current master key version
	return version < ms.NewMasterKeyVersion
}

// runMigration chạy migration process
func (ms *MigrationService) runMigration(filesToMigrate []string) {
	defer func() {
		ms.mutex.Lock()
		ms.running = false
		ms.mutex.Unlock()
	}()

	paused := false

	// fs, err := NewFSWithConfig(ms.CipherDir, ms.CryptoCore.MasterKey, nil, false)
	// if err != nil {
	// 	fmt.Printf("Cannot create filesystem: %v\n", err)
	// 	return
	// }

	for i := 0; i < len(filesToMigrate); i += ms.BatchSize {
		// Check for stop signal
		select {
		case <-ms.stopChan:
			return
		case <-ms.pauseChan:
			paused = true
		default:
		}

		// Handle pause/resume
		if paused {
			select {
			case <-ms.stopChan:
				return
			case <-ms.resumeChan:
				paused = false
			}
		}

		// Process batch
		end := i + ms.BatchSize
		if end > len(filesToMigrate) {
			end = len(filesToMigrate)
		}

		// batch := filesToMigrate[i:end]
		// ms.processBatch(fs, batch)

		// Sleep between batches để không overload system
		time.Sleep(ms.SleepInterval)
	}
}

// processBatch xử lý một batch files
// func (ms *MigrationService) processBatch(fs *FS, batch []string) {
// 	for _, filePath := range batch {
// 		ms.mutex.Lock()
// 		ms.CurrentFile = filePath
// 		ms.mutex.Unlock()

// 		success := false
// 		var lastErr error
// 		for retry := 0; retry < ms.MaxRetries; retry++ {
// 			err := fs.migrateOldData(filePath, ms.NewMasterKey, ms.NewMasterKeyVersion)
// 			if err == nil {
// 				success = true
// 				break
// 			}
// 			lastErr = err

// 			// Retry after short delay
// 			time.Sleep(50 * time.Millisecond)
// 		}

// 		ms.mutex.Lock()
// 		if success {
// 			ms.MigratedFiles++
// 			fmt.Printf("✓ Migrated: %s\n", filePath)
// 		} else {
// 			ms.FailedFiles++
// 			fmt.Printf("✗ Failed: %s (error: %v)\n", filePath, lastErr)
// 		}
// 		ms.mutex.Unlock()
// 	}
// }

// GetStats trả về migration statistics
func (ms *MigrationService) GetStats() map[string]interface{} {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	progress := 0.0
	if ms.TotalFiles > 0 {
		progress = float64(ms.MigratedFiles) / float64(ms.TotalFiles) * 100
	}

	return map[string]interface{}{
		"total_files":    ms.TotalFiles,
		"migrated_files": ms.MigratedFiles,
		"failed_files":   ms.FailedFiles,
		"current_file":   ms.CurrentFile,
		"progress_pct":   progress,
		"running":        ms.running,
		"batch_size":     ms.BatchSize,
		"sleep_interval": ms.SleepInterval,
	}
}

// SetBatchSize cài đặt batch size
func (ms *MigrationService) SetBatchSize(size int) {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	if size > 0 {
		ms.BatchSize = size
	}
}

// SetSleepInterval cài đặt sleep interval
func (ms *MigrationService) SetSleepInterval(interval time.Duration) {
	ms.mutex.Lock()
	defer ms.mutex.Unlock()

	ms.SleepInterval = interval
}

// EstimateTimeRemaining ước tính thời gian còn lại
func (ms *MigrationService) EstimateTimeRemaining() time.Duration {
	ms.mutex.RLock()
	defer ms.mutex.RUnlock()

	if ms.MigratedFiles == 0 || !ms.running {
		return 0
	}

	// Tính average time per file (rough estimate)
	// Giả sử mỗi file mất ~1ms để migrate
	remainingFiles := ms.TotalFiles - ms.MigratedFiles
	avgTimePerFile := 1 * time.Millisecond

	return time.Duration(remainingFiles) * avgTimePerFile
}

// ===================== Selective Migration =====================

// MigrationFilter định nghĩa filter cho selective migration
type MigrationFilter struct {
	// File patterns
	IncludePatterns []string // Glob patterns to include
	ExcludePatterns []string // Glob patterns to exclude

	// File properties
	MinSize        int64     // Minimum file size
	MaxSize        int64     // Maximum file size
	ModifiedAfter  time.Time // Only files modified after this time
	ModifiedBefore time.Time // Only files modified before this time

	// Key version
	FromKeyVersion uint16 // Migrate from this key version
	ToKeyVersion   uint16 // Migrate to this key version
}

// SelectiveMigration migrate chỉ files thỏa mãn filter
func (ms *MigrationService) SelectiveMigration(filter MigrationFilter) error {
	filesToMigrate, err := ms.findFilesWithFilter(filter)
	if err != nil {
		return fmt.Errorf("cannot find files with filter: %v", err)
	}

	ms.TotalFiles = len(filesToMigrate)
	if ms.TotalFiles == 0 {
		return fmt.Errorf("no files match the filter criteria")
	}

	// Start migration
	ms.mutex.Lock()
	ms.running = true
	ms.mutex.Unlock()

	go ms.runMigration(filesToMigrate)
	return nil
}

// findFilesWithFilter tìm files theo filter
func (ms *MigrationService) findFilesWithFilter(filter MigrationFilter) ([]string, error) {
	var filesToMigrate []string

	err := filepath.Walk(ms.CipherDir, func(path string, info os.FileInfo, err error) error {
		if err != nil {
			return err
		}

		// Skip directories, special files, và backup files
		if info.IsDir() || crypto.IsContainFilename(info.Name()) || isBackupFile(info.Name()) {
			return nil
		}

		// Apply filter
		if ms.matchesFilter(path, info, filter) {
			filesToMigrate = append(filesToMigrate, path)
		}

		return nil
	})

	return filesToMigrate, err
}

// matchesFilter kiểm tra xem file có match filter không
func (ms *MigrationService) matchesFilter(filePath string, info os.FileInfo, filter MigrationFilter) bool {
	// Check if envelope encrypted
	if !IsEnvelopeEncryptedFile(filePath) {
		return false
	}

	// Check key version
	currentVersion, err := GetFileKeyVersion(filePath)
	if err != nil {
		return false
	}

	if filter.FromKeyVersion > 0 && currentVersion != filter.FromKeyVersion {
		return false
	}

	// Check file size
	if filter.MinSize > 0 && info.Size() < filter.MinSize {
		return false
	}
	if filter.MaxSize > 0 && info.Size() > filter.MaxSize {
		return false
	}

	// Check modification time
	if !filter.ModifiedAfter.IsZero() && info.ModTime().Before(filter.ModifiedAfter) {
		return false
	}
	if !filter.ModifiedBefore.IsZero() && info.ModTime().After(filter.ModifiedBefore) {
		return false
	}

	// Check include patterns
	if len(filter.IncludePatterns) > 0 {
		matched := false
		for _, pattern := range filter.IncludePatterns {
			if match, _ := filepath.Match(pattern, filepath.Base(filePath)); match {
				matched = true
				break
			}
		}
		if !matched {
			return false
		}
	}

	// Check exclude patterns
	for _, pattern := range filter.ExcludePatterns {
		if match, _ := filepath.Match(pattern, filepath.Base(filePath)); match {
			return false
		}
	}

	return true
}

// isBackupFile kiểm tra xem file có phải backup file không
func isBackupFile(filename string) bool {
	// Check if filename contains .backup. pattern
	return strings.Contains(filename, ".backup.")
}

// ===================== Lazy Migration =====================

// shouldMigrateFile kiểm tra xem file có cần migrate không
func (f *FS) shouldMigrateFile(cipherData []byte) (uint16, bool) {
	if len(cipherData) < 4 {
		return 0, false
	}

	// Check version và master key version
	version := uint16(cipherData[0]) | uint16(cipherData[1])<<8
	if version != 3 {
		return 0, false // Chỉ migrate envelope encrypted files
	}

	// Parse header để check master key version
	if len(cipherData) < crypto.ENVELOPE_HEADER_SIZE {
		return 0, false
	}

	header, err := crypto.ParseEnvelopeFileHeader(cipherData[:crypto.ENVELOPE_HEADER_SIZE])
	if err != nil {
		return 0, false
	}

	// Migrate nếu file đang dùng master key version cũ
	return header.MasterKeyVersion, header.MasterKeyVersion < f.CryptoCore.MasterKeyVersion
}

// migrateFileOnAccess migrate file khi được access
func (f *FS) migrateFileOnAccess(filePath string) {
	if f.Debug {
		fmt.Printf("Lazy migration: %s\n", filePath)
	}

	// Rotate key in background (non-blocking)
	go func() {
		newFS := f
		MigrateOldDatas(newFS, filePath)
	}()
}

// IsEnvelopeEncryptedFile kiểm tra xem file có phải envelope encrypted không
func IsEnvelopeEncryptedFile(filePath string) bool {
	data, err := os.ReadFile(filePath)
	if err != nil || len(data) < 2 {
		return false
	}

	// Check version trong header
	version := uint16(data[0]) | uint16(data[1])<<8
	return version == 3 // Version 3 = envelope encryption
}

// GetFileKeyVersion lấy master key version từ envelope encrypted file
func GetFileKeyVersion(filePath string) (uint16, error) {
	data, err := os.ReadFile(filePath)
	if err != nil {
		return 0, fmt.Errorf("cannot read file: %v", err)
	}

	if len(data) < crypto.ENVELOPE_HEADER_SIZE {
		return 0, fmt.Errorf("file too short for envelope encryption")
	}

	header, err := crypto.ParseEnvelopeFileHeader(data[:crypto.ENVELOPE_HEADER_SIZE])
	if err != nil {
		return 0, fmt.Errorf("cannot parse envelope header: %v", err)
	}

	return header.MasterKeyVersion, nil
}
