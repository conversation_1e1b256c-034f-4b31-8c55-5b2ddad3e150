{
    "version": "0.2.0",
    "configurations": [
        // {
        //     "name": "Python Debugger: Current File",
        //     "type": "debugpy",
        //     "request": "launch",
        //     "program": "${file}",
        //     "console": "integratedTerminal"
        // },
        {
            "name": "Launch",
            "type": "go",
            "request": "launch",
            "mode": "auto",
            "console": "integratedTerminal",
            // "program": "${workspaceFolder}/main.go",
            "program": "/Users/<USER>/Documents/Learn/private/go/gocryptfs/main.go",
            // "program": "${file}",
            "env": {
                "debug-local": "true",
                "protocol": "HTTP",
                "env": "stg",
            },
            "args": [
                // init
                // "-init", "./demo/cipher",

                // start
                "./demo/cipher", "./demo/plain",

                // rotate
                // "-rotate", "./demo/cipher"

                // migrate
                // "-migrate", "./demo/cipher"
            ]
        }
    ]
}