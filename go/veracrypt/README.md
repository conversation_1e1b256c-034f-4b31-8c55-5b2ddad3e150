# VeraCrypt-like Tool với FUSE Support

Ứng dụng mã hóa file và container giống VeraCrypt được viết bằng Go, hỗ trợ FUSE filesystem.

## 🔐 Tính năng

### File Encryption/Decryption
- **AES-256-GCM**: Mã hóa chuẩn quân sự với authenticated encryption
- **PBKDF2**: Key derivation với 100,000 iterations và SHA-256
- **Random Salt & Nonce**: Mỗi file có salt và nonce riêng biệt
- **Password Protection**: Nhập password ẩn (không hiển thị trên terminal)

### Container Support với FUSE
- **Encrypted Containers**: Tạo container mã hóa với kích thước tùy chỉnh
- **Virtual Filesystem**: Mount container như một ổ đĩa thông thường
- **Real-time Encryption**: Dữ liệu được mã hóa/giải mã tự động khi đọc/ghi
- **Cross-platform**: Hoạt động trên Linux với FUSE

## 📦 Cài đặt

### Yêu cầu hệ thống
- Go 1.19+
- FUSE library (Linux: `libfuse-dev`, Ubuntu: `sudo apt install libfuse-dev`)

### Build từ source
```bash
git clone <repository>
cd veracrypt
go mod tidy
go build -o veracrypt main.go
```

## 🚀 Sử dụng

### 1. Mã hóa/Giải mã File

```bash
# Mã hóa file
./veracrypt encrypt document.txt
./veracrypt e secret.pdf encrypted_secret.pdf

# Giải mã file
./veracrypt decrypt document.txt.encrypted
./veracrypt d encrypted_secret.pdf decrypted_secret.pdf
```

### 2. Tạo Encrypted Container

```bash
# Tạo container 100MB (mặc định)
./veracrypt create-container vault.vc

# Tạo container 500MB
./veracrypt create-container vault.vc 500
```

### 3. Mount Container với FUSE

```bash
# Tạo mount point
mkdir ./mnt

# Mount container
./veracrypt mount vault.vc ./mnt

# Sử dụng như ổ đĩa thông thường
echo "Secret data" > ./mnt/secret.txt
ls -la ./mnt/

# Unmount (Ctrl+C trong terminal chạy mount)
```

## 🔧 Các lệnh

| Lệnh | Mô tả | Ví dụ |
|------|-------|-------|
| `encrypt\|e` | Mã hóa file | `./veracrypt e file.txt` |
| `decrypt\|d` | Giải mã file | `./veracrypt d file.txt.encrypted` |
| `create-container\|cc` | Tạo container | `./veracrypt cc vault.vc 100` |
| `mount\|m` | Mount container | `./veracrypt m vault.vc ./mnt` |
| `help\|h` | Hiển thị help | `./veracrypt help` |

## 🛡️ Bảo mật

### Thuật toán mã hóa
- **AES-256**: Advanced Encryption Standard với key 256-bit
- **GCM Mode**: Galois/Counter Mode với authenticated encryption
- **PBKDF2**: Password-Based Key Derivation Function 2
- **SHA-256**: Secure Hash Algorithm 256-bit

### Cấu trúc file mã hóa
```
[32 bytes Salt][12 bytes Nonce][Encrypted Data + Auth Tag]
```

### Container format
```
[32 bytes Salt][Encrypted Filesystem Data]
```

## 🧪 Testing

```bash
# Test file encryption/decryption
echo "Secret message" > test.txt
./veracrypt encrypt test.txt
./veracrypt decrypt test.txt.encrypted

# Test container
./veracrypt create-container test.vc 10
mkdir mnt
./veracrypt mount test.vc ./mnt
```

## ⚠️ Lưu ý

1. **Backup Password**: Mất password = mất dữ liệu vĩnh viễn
2. **FUSE Requirements**: Cần cài đặt FUSE library
3. **Root Permissions**: Một số hệ thống cần sudo để mount
4. **Container Size**: Container có kích thước cố định khi tạo

## 🔄 So sánh với VeraCrypt

| Tính năng | VeraCrypt | Tool này |
|-----------|-----------|----------|
| File Encryption | ✅ | ✅ |
| Container Support | ✅ | ✅ |
| FUSE Mounting | ✅ | ✅ |
| AES-256 | ✅ | ✅ |
| Cross-platform | ✅ | Linux only |
| GUI | ✅ | CLI only |
| Hidden Volumes | ✅ | ❌ |
| System Encryption | ✅ | ❌ |

## 📝 License

MIT License - Xem file LICENSE để biết thêm chi tiết.
