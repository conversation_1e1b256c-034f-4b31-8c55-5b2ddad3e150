#!/bin/bash

# check file /Users/<USER>/Documents/Learn/private/go/webdav/demo exist, if not, call make build
if [ ! -f "/Users/<USER>/Documents/Learn/private/go/webdav/demo" ]; then
    echo "Building WebDAV server..."
    cd /Users/<USER>/Documents/Learn/private/go/webdav
    make build
fi

cd /Users/<USER>/Documents/Learn/private/go/webdav/ui

# WebDAV Controller Startup Script

echo "🚀 Starting WebDAV Controller..."

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Shutting down services..."
    
    # Kill backend if running
    if [ ! -z "$BACKEND_PID" ]; then
        echo "Stopping backend server..."
        kill $BACKEND_PID 2>/dev/null
    fi
    
    # Kill frontend if running
    if [ ! -z "$FRONTEND_PID" ]; then
        echo "Stopping frontend server..."
        kill $FRONTEND_PID 2>/dev/null
    fi
    
    echo "✅ All services stopped"
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Check if we're in the right directory
if [ ! -f "package.json" ]; then
    echo "❌ Error: Please run this script from the ui directory"
    exit 1
fi

# Check if demo executable exists
if [ ! -f "../demo" ]; then
    echo "❌ Error: WebDAV demo executable not found at ../demo"
    echo "Please build the WebDAV server first:"
    echo "  cd .. && make build"
    exit 1
fi

# Install frontend dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "📦 Installing frontend dependencies..."
    npm install
fi

# Install backend dependencies if needed
if [ ! -d "backend/node_modules" ]; then
    echo "📦 Installing backend dependencies..."
    cd backend
    npm install
    cd ..
fi

echo ""
echo "🔧 Starting backend API server..."

# Start backend server
cd backend
npm start &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 3

# Check if backend is running
if ! kill -0 $BACKEND_PID 2>/dev/null; then
    echo "❌ Failed to start backend server"
    exit 1
fi

echo "✅ Backend server started (PID: $BACKEND_PID)"
echo ""
echo "🎨 Starting frontend development server..."

# Start frontend server
npm start &
FRONTEND_PID=$!

# Wait for frontend to start
echo "⏳ Waiting for frontend to start..."
sleep 5

# Check if frontend is running
if ! kill -0 $FRONTEND_PID 2>/dev/null; then
    echo "❌ Failed to start frontend server"
    cleanup
    exit 1
fi

echo "✅ Frontend server started (PID: $FRONTEND_PID)"
echo ""
echo "🎉 WebDAV Controller is ready!"
echo ""
echo "📱 Frontend: http://localhost:3000"
echo "🔌 Backend API: http://localhost:3001"
echo "🌐 WebDAV Server: https://localhost:8443 (when started)"
echo ""
echo "Press Ctrl+C to stop all services"

# Wait for user to stop
wait
