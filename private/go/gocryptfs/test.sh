#!/bin/bash

# Comprehensive test runner for gocryptfs
# Usage: ./run_tests.sh [test_type] [options]

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Default values
VERBOSE=false
COVERAGE=false
RACE=false
BENCH_TIME="1s"
TEST_TIMEOUT="10m"

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
Usage: $0 [test_type] [options]

Test Types:
    unit        Run unit tests only
    integration Run integration tests only
    benchmarks  Run benchmark tests only
    security    Run security tests only
    all         Run all tests (default)

Options:
    -v, --verbose       Enable verbose output
    -c, --coverage      Generate coverage report
    -r, --race          Enable race detection
    -t, --timeout TIME  Set test timeout (default: 10m)
    -b, --bench-time TIME Set benchmark time (default: 1s)
    -h, --help          Show this help message

Examples:
    $0 unit -v -c                    # Run unit tests with verbose output and coverage
    $0 benchmarks -b 5s              # Run benchmarks for 5 seconds each
    $0 all -r -c                     # Run all tests with race detection and coverage
    $0 security --verbose            # Run security tests with verbose output
EOF
}

# Parse command line arguments
TEST_TYPE="all"
while [[ $# -gt 0 ]]; do
    case $1 in
        unit|integration|benchmarks|security|all)
            TEST_TYPE="$1"
            shift
            ;;
        -v|--verbose)
            VERBOSE=true
            shift
            ;;
        -c|--coverage)
            COVERAGE=true
            shift
            ;;
        -r|--race)
            RACE=true
            shift
            ;;
        -t|--timeout)
            TEST_TIMEOUT="$2"
            shift 2
            ;;
        -b|--bench-time)
            BENCH_TIME="$2"
            shift 2
            ;;
        -h|--help)
            show_usage
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            show_usage
            exit 1
            ;;
    esac
done

# Build test flags
TEST_FLAGS=""
if [ "$VERBOSE" = true ]; then
    TEST_FLAGS="$TEST_FLAGS -v"
fi

if [ "$RACE" = true ]; then
    TEST_FLAGS="$TEST_FLAGS -race"
fi

TEST_FLAGS="$TEST_FLAGS -timeout $TEST_TIMEOUT"

# Coverage flags
COVERAGE_FLAGS=""
if [ "$COVERAGE" = true ]; then
    COVERAGE_FLAGS="-coverprofile=coverage.out -covermode=atomic"
fi

# Change to project root
cd "$(dirname "$0")/.."

print_status "Starting gocryptfs test suite..."
print_status "Test type: $TEST_TYPE"
print_status "Flags: $TEST_FLAGS"

# Function to run unit tests
run_unit_tests() {
    print_status "Running unit tests..."
    
    # Run existing crypto tests
    go test $TEST_FLAGS $COVERAGE_FLAGS ./internal/crypto/
    
    # Run new extended unit tests
    go test $TEST_FLAGS ./tests/unit/
    
    print_success "Unit tests completed"
}

# Function to run integration tests
run_integration_tests() {
    print_status "Running integration tests..."
    
    go test $TEST_FLAGS ./tests/integration/
    
    print_success "Integration tests completed"
}

# Function to run benchmark tests
run_benchmark_tests() {
    print_status "Running benchmark tests..."
    
    # Run benchmarks with specified time
    go test -bench=. -benchtime=$BENCH_TIME -benchmem ./tests/benchmarks/
    
    print_success "Benchmark tests completed"
}

# Function to run security tests
run_security_tests() {
    print_status "Running security tests..."
    
    go test $TEST_FLAGS ./tests/security/
    
    print_success "Security tests completed"
}

# Function to generate coverage report
generate_coverage_report() {
    if [ "$COVERAGE" = true ] && [ -f "coverage.out" ]; then
        print_status "Generating coverage report..."
        
        # Generate HTML coverage report
        go tool cover -html=coverage.out -o coverage.html
        
        # Show coverage summary
        go tool cover -func=coverage.out | tail -1
        
        print_success "Coverage report generated: coverage.html"
    fi
}

# Function to run all tests
run_all_tests() {
    print_status "Running all test suites..."
    
    run_unit_tests
    run_integration_tests
    run_security_tests
    
    # Run benchmarks last (they take longer)
    print_status "Running quick benchmarks..."
    go test -bench=. -benchtime=100ms -benchmem ./tests/benchmarks/ | head -20
    
    print_success "All tests completed"
}

# Main execution
case $TEST_TYPE in
    unit)
        run_unit_tests
        ;;
    integration)
        run_integration_tests
        ;;
    benchmarks)
        run_benchmark_tests
        ;;
    security)
        run_security_tests
        ;;
    all)
        run_all_tests
        ;;
esac

# Generate coverage report if requested
generate_coverage_report

# Final status
if [ $? -eq 0 ]; then
    print_success "Test suite completed successfully!"
else
    print_error "Test suite failed!"
    exit 1
fi

# Show test summary
print_status "Test Summary:"
echo "  Test Type: $TEST_TYPE"
echo "  Verbose: $VERBOSE"
echo "  Coverage: $COVERAGE"
echo "  Race Detection: $RACE"
echo "  Timeout: $TEST_TIMEOUT"
if [ "$TEST_TYPE" = "benchmarks" ] || [ "$TEST_TYPE" = "all" ]; then
    echo "  Benchmark Time: $BENCH_TIME"
fi

# Show next steps
print_status "Next steps:"
echo "  - Review test results above"
if [ "$COVERAGE" = true ]; then
    echo "  - Open coverage.html in browser to view coverage report"
fi
echo "  - Run specific test types for focused testing"
echo "  - Use -v flag for verbose output during debugging"
