package crypto

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"fmt"
)

const (
	// Envelope Encryption constants
	// DEK_SIZE là kích thước Data Encryption Key (32 bytes)
	DEK_SIZE = 32
	// ENVELOPE_HEADER_SIZE là kích thước header cho envelope encryption
	// 2 bytes version + 2 bytes master key version + 16 bytes file ID + 48 bytes encrypted DEK (32+16) + 12 bytes DEK IV
	ENVELOPE_HEADER_SIZE = 80
	// DEK_IV_SIZE là kích thước IV cho DEK encryption (12 bytes)
	DEK_IV_SIZE = 12
)

// EnvelopeFileHeader chứa thông tin header cho envelope encryption
type EnvelopeFileHeader struct {
	Version          uint16 // File format version (3 for envelope encryption)
	MasterKeyVersion uint16 // Version của master key đượ<PERSON> sử dụng
	FileID           []byte // File identifier (16 bytes)
	EncryptedDEK     []byte // DEK đư<PERSON> mã hóa bởi master key (32 bytes)
	DEKIV            []byte // IV cho việc mã hóa DEK (12 bytes)
}

// DEK (Data Encryption Key) chứa thông tin về khóa mã hóa dữ liệu
type DEK struct {
	Key    []byte // 32 bytes AES-256 key
	FileID []byte // File ID mà DEK này thuộc về
}

// -------------------- Header ------------------------

// createFileHeader tạo file header với encryption mode phù hợp
func (cc *CryptoCore) CreateFileHeaderWithSerialize() ([]byte, error) {
	header, err := cc.CreateEnvelopeFileHeader(cc.MasterKeyVersion)
	if err != nil {
		return nil, err
	}
	return header.SerializeEnvelopeFileHeader(), nil
}

// CreateEnvelopeFileHeader tạo header cho envelope encryption
func (cc *CryptoCore) CreateEnvelopeFileHeader(masterKeyVersion uint16) (*EnvelopeFileHeader, error) {
	// Tạo file ID
	fileID := make([]byte, FILE_ID_SIZE)
	if _, err := rand.Read(fileID); err != nil {
		return nil, fmt.Errorf("cannot generate file ID: %v", err)
	}

	// ------------------------------------------------------------

	// Tạo DEK (Data Encryption Key)
	dek := make([]byte, DEK_SIZE)
	if _, err := rand.Read(dek); err != nil {
		return nil, fmt.Errorf("cannot generate DEK: %v", err)
	}

	// Tạo IV cho việc mã hóa DEK
	dekIV := make([]byte, DEK_IV_SIZE)
	if _, err := rand.Read(dekIV); err != nil {
		return nil, fmt.Errorf("cannot generate DEK IV: %v", err)
	}

	// Mã hóa DEK bằng master key
	encryptedDEK, err := cc.encryptDEK(dek, dekIV)
	if err != nil {
		return nil, fmt.Errorf("cannot encrypt DEK: %v", err)
	}

	// ------------------------------------------------------------

	return &EnvelopeFileHeader{
		Version:          3, // Version 3 cho envelope encryption
		MasterKeyVersion: masterKeyVersion,
		FileID:           fileID,
		EncryptedDEK:     encryptedDEK,
		DEKIV:            dekIV,
	}, nil
}

// SerializeEnvelopeFileHeader serialize envelope file header thành bytes
func (efh *EnvelopeFileHeader) SerializeEnvelopeFileHeader() []byte {
	headerB := make([]byte, ENVELOPE_HEADER_SIZE)

	// Version (2 bytes, little endian)
	headerB[0] = byte(efh.Version)
	headerB[1] = byte(efh.Version >> 8)

	// Master Key Version (2 bytes, little endian)
	headerB[2] = byte(efh.MasterKeyVersion)
	headerB[3] = byte(efh.MasterKeyVersion >> 8)

	// File ID (16 bytes)
	copy(headerB[4:20], efh.FileID)

	// Encrypted DEK (48 bytes = 32 bytes DEK + 16 bytes tag)
	copy(headerB[20:68], efh.EncryptedDEK)

	// DEK IV (12 bytes)
	copy(headerB[68:80], efh.DEKIV)

	return headerB
}

// ParseEnvelopeFileHeader parse envelope file header từ bytes
func ParseEnvelopeFileHeader(data []byte) (*EnvelopeFileHeader, error) {
	if len(data) < ENVELOPE_HEADER_SIZE {
		return nil, fmt.Errorf("envelope header too short: %d bytes", len(data))
	}

	version := uint16(data[0]) | uint16(data[1])<<8
	masterKeyVersion := uint16(data[2]) | uint16(data[3])<<8

	fileID := make([]byte, FILE_ID_SIZE)
	copy(fileID, data[4:20])

	encryptedDEK := make([]byte, DEK_SIZE+TAG_SIZE) // 48 bytes
	copy(encryptedDEK, data[20:68])

	dekIV := make([]byte, DEK_IV_SIZE)
	copy(dekIV, data[68:80])

	return &EnvelopeFileHeader{
		Version:          version,
		MasterKeyVersion: masterKeyVersion,
		FileID:           fileID,
		EncryptedDEK:     encryptedDEK,
		DEKIV:            dekIV,
	}, nil
}

// encryptDEK mã hóa DEK bằng master key
func (cc *CryptoCore) encryptDEK(dek []byte, iv []byte) ([]byte, error) {
	if len(dek) != DEK_SIZE {
		return nil, fmt.Errorf("invalid DEK size: %d", len(dek))
	}
	if len(iv) != DEK_IV_SIZE {
		return nil, fmt.Errorf("invalid DEK IV size: %d", len(iv))
	}

	// Sử dụng master key để mã hóa DEK
	encryptedDEK := cc.AESGCMCipher.Seal(nil, iv, dek, nil)
	return encryptedDEK, nil
}

// decryptDEK giải mã DEK bằng master key
func (cc *CryptoCore) decryptDEK(encryptedDEK []byte, iv []byte) ([]byte, error) {
	if len(iv) != DEK_IV_SIZE {
		return nil, fmt.Errorf("invalid DEK IV size: %d", len(iv))
	}

	// Giải mã DEK bằng master key
	dek, err := cc.AESGCMCipher.Open(nil, iv, encryptedDEK, nil)
	if err != nil {
		return nil, fmt.Errorf("cannot decrypt DEK: %v", err)
	}

	if len(dek) != DEK_SIZE {
		return nil, fmt.Errorf("decrypted DEK has invalid size: %d", len(dek))
	}

	return dek, nil
}

// createDEKCipherGCM tạo AES-GCM cipher từ DEK
func createDEKCipherGCM(dek []byte) (cipher.AEAD, error) {
	if len(dek) != DEK_SIZE {
		return nil, fmt.Errorf("invalid DEK size: %d", len(dek))
	}

	// Tạo AES cipher từ DEK
	block, err := aes.NewCipher(dek)
	if err != nil {
		return nil, fmt.Errorf("cannot create AES cipher from DEK: %v", err)
	}

	// Tạo GCM mode
	gcm, err := cipher.NewGCM(block)
	if err != nil {
		return nil, fmt.Errorf("cannot create GCM from DEK: %v", err)
	}

	return gcm, nil
}

// -------------------- Block -----------------------

// encryptBlockWithDEK mã hóa một block dữ liệu bằng DEK
func (cc *CryptoCore) encryptBlockWithDEK(plaintext []byte, blockNo uint64, fileID []byte, dekCipher cipher.AEAD) ([]byte, error) {
	if len(plaintext) > BLOCK_SIZE {
		return nil, fmt.Errorf("block too large: %d bytes", len(plaintext))
	}

	// Tạo nonce từ file ID và block number (same logic as original)
	nonce := cc.createNonce(fileID, blockNo)

	// Mã hóa bằng DEK cipher
	ciphertext := dekCipher.Seal(nil, nonce, plaintext, nil)

	return ciphertext, nil
}

// decryptBlockWithDEK giải mã một block dữ liệu bằng DEK
func (cc *CryptoCore) decryptBlockWithDEK(ciphertext []byte, blockNo uint64, fileID []byte, dekCipher cipher.AEAD) ([]byte, error) {
	if len(ciphertext) < TAG_SIZE {
		return nil, fmt.Errorf("ciphertext too short: %d bytes", len(ciphertext))
	}

	// Tạo nonce từ file ID và block number (same logic as original)
	nonce := cc.createNonce(fileID, blockNo)

	// Giải mã bằng DEK cipher
	plaintext, err := dekCipher.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, fmt.Errorf("decryption failed: %v", err)
	}

	return plaintext, nil
}

// -------------------- File ------------------------

// EncryptFileEnvelope mã hóa file sử dụng envelope encryption
func (cc *CryptoCore) EncryptFileEnvelope(plaintext []byte, masterKeyVersion uint16) ([]byte, error) {
	// Tạo envelope file header
	header, err := cc.CreateEnvelopeFileHeader(masterKeyVersion)
	if err != nil {
		return nil, err
	}

	// Giải mã DEK từ header để sử dụng
	dek, err := cc.decryptDEK(header.EncryptedDEK, header.DEKIV)
	if err != nil {
		return nil, fmt.Errorf("cannot decrypt DEK: %v", err)
	}

	// Tạo cipher từ DEK
	dekCipher, err := createDEKCipherGCM(dek)
	if err != nil {
		return nil, err
	}

	// Serialize header
	headerBytes := header.SerializeEnvelopeFileHeader()

	// Tính số blocks cần thiết
	numBlocks := (len(plaintext) + BLOCK_SIZE - 1) / BLOCK_SIZE
	if numBlocks == 0 {
		// File rỗng, chỉ có header
		return headerBytes, nil
	}

	// Mã hóa từng block bằng DEK
	var ciphertext []byte
	ciphertext = append(ciphertext, headerBytes...)

	for i := 0; i < numBlocks; i++ {
		start := i * BLOCK_SIZE
		end := start + BLOCK_SIZE
		if end > len(plaintext) {
			end = len(plaintext)
		}

		blockData := plaintext[start:end]
		encryptedBlock, err := cc.encryptBlockWithDEK(blockData, uint64(i), header.FileID, dekCipher)
		if err != nil {
			return nil, fmt.Errorf("cannot encrypt block %d: %v", i, err)
		}

		ciphertext = append(ciphertext, encryptedBlock...)
	}

	return ciphertext, nil
}

// DecryptFileEnvelope giải mã file sử dụng envelope encryption
func (cc *CryptoCore) DecryptFileEnvelope(ciphertext []byte) ([]byte, error) {
	if len(ciphertext) < ENVELOPE_HEADER_SIZE {
		return nil, fmt.Errorf("file too short: %d bytes", len(ciphertext))
	}

	// Parse envelope header
	header, err := ParseEnvelopeFileHeader(ciphertext[:ENVELOPE_HEADER_SIZE])
	if err != nil {
		return nil, fmt.Errorf("cannot parse envelope header: %v", err)
	}

	// Kiểm tra version
	if header.Version != 3 {
		return nil, fmt.Errorf("unsupported envelope file version: %d", header.Version)
	}

	// Giải mã DEK
	dek, err := cc.decryptDEK(header.EncryptedDEK, header.DEKIV)
	if err != nil {
		return nil, fmt.Errorf("cannot decrypt DEK: %v", err)
	}

	// Tạo cipher từ DEK
	dekCipher, err := createDEKCipherGCM(dek)
	if err != nil {
		return nil, err
	}

	// Nếu file chỉ có header (file rỗng)
	if len(ciphertext) == ENVELOPE_HEADER_SIZE {
		return []byte{}, nil
	}

	// Giải mã từng block bằng DEK
	var plaintext []byte
	blockData := ciphertext[ENVELOPE_HEADER_SIZE:]
	blockNo := uint64(0)

	for len(blockData) > 0 {
		// Tính kích thước block hiện tại
		maxBlockSize := BLOCK_SIZE + TAG_SIZE
		currentBlockSize := maxBlockSize
		if len(blockData) < maxBlockSize {
			currentBlockSize = len(blockData)
		}

		// Giải mã block bằng DEK
		decryptedBlock, err := cc.decryptBlockWithDEK(blockData[:currentBlockSize], blockNo, header.FileID, dekCipher)
		if err != nil {
			return nil, fmt.Errorf("cannot decrypt block %d: %v", blockNo, err)
		}

		plaintext = append(plaintext, decryptedBlock...)
		blockData = blockData[currentBlockSize:]
		blockNo++
	}

	return plaintext, nil
}

// // EncryptReaderEnvelope mã hóa dữ liệu từ reader sử dụng envelope encryption
// func (cc *CryptoCore) EncryptReaderEnvelope(reader io.Reader, masterKeyVersion uint16) ([]byte, error) {
// 	data, err := io.ReadAll(reader)
// 	if err != nil {
// 		return nil, fmt.Errorf("cannot read data: %v", err)
// 	}

// 	return cc.EncryptFileEnvelope(data, masterKeyVersion)
// }

// // DecryptToWriterEnvelope giải mã envelope encrypted data và ghi vào writer
// func (cc *CryptoCore) DecryptToWriterEnvelope(ciphertext []byte, writer io.Writer) error {
// 	plaintext, err := cc.DecryptFileEnvelope(ciphertext)
// 	if err != nil {
// 		return err
// 	}

// 	_, err = writer.Write(plaintext)
// 	return err
// }
