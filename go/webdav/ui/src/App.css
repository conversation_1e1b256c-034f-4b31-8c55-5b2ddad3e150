.App {
  text-align: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.App-header {
  padding: 20px;
  color: white;
  max-width: 800px;
  margin: 0 auto;
}

.App-header h1 {
  margin-bottom: 30px;
  font-size: 2.5rem;
  text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

/* Status Section */
.status-section {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 30px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  backdrop-filter: blur(10px);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 1.2rem;
  font-weight: bold;
}

.status-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  display: inline-block;
  box-shadow: 0 0 10px rgba(255,255,255,0.5);
}

.refresh-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 8px 16px;
  border-radius: 5px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.refresh-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.refresh-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

/* Control Section */
.control-section {
  display: flex;
  justify-content: center;
  gap: 20px;
  margin-bottom: 30px;
}

.start-btn, .stop-btn {
  padding: 15px 30px;
  font-size: 1.1rem;
  font-weight: bold;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 150px;
}

.start-btn {
  background: linear-gradient(45deg, #4CAF50, #45a049);
  color: white;
  box-shadow: 0 4px 15px rgba(76, 175, 80, 0.3);
}

.start-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(76, 175, 80, 0.4);
}

.stop-btn {
  background: linear-gradient(45deg, #f44336, #da190b);
  color: white;
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.stop-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(244, 67, 54, 0.4);
}

.start-btn:disabled, .stop-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

/* Error Message */
.error-message {
  background: rgba(244, 67, 54, 0.2);
  border: 1px solid rgba(244, 67, 54, 0.5);
  color: #ffcdd2;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  font-weight: bold;
}

/* Info Section */
.info-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  margin-bottom: 30px;
  backdrop-filter: blur(10px);
}

.info-section h3 {
  margin-bottom: 15px;
  color: #e3f2fd;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 10px;
  text-align: left;
}

.info-item {
  padding: 10px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 5px;
  border-left: 3px solid #61dafb;
}

.info-item strong {
  color: #81c784;
}

/* Logs Section */
.logs-section {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 10px;
  padding: 20px;
  backdrop-filter: blur(10px);
}

.logs-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.logs-header h3 {
  margin: 0;
  color: #e3f2fd;
}

.clear-logs-btn {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.9rem;
  transition: all 0.3s ease;
}

.clear-logs-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.logs-container {
  max-height: 300px;
  overflow-y: auto;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 5px;
  padding: 10px;
  text-align: left;
}

.no-logs {
  text-align: center;
  color: #bbb;
  font-style: italic;
  padding: 20px;
}

.log-entry {
  padding: 8px;
  margin-bottom: 5px;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

.log-info {
  background: rgba(33, 150, 243, 0.2);
  border-left: 3px solid #2196F3;
}

.log-success {
  background: rgba(76, 175, 80, 0.2);
  border-left: 3px solid #4CAF50;
}

.log-error {
  background: rgba(244, 67, 54, 0.2);
  border-left: 3px solid #f44336;
}

.log-timestamp {
  color: #bbb;
  margin-right: 10px;
}

.log-message {
  color: white;
}

/* Responsive Design */
@media (max-width: 768px) {
  .App-header {
    padding: 15px;
  }

  .App-header h1 {
    font-size: 2rem;
  }

  .control-section {
    flex-direction: column;
    align-items: center;
  }

  .start-btn, .stop-btn {
    width: 100%;
    max-width: 250px;
  }

  .status-section {
    flex-direction: column;
    gap: 15px;
  }

  .info-grid {
    grid-template-columns: 1fr;
  }
}
