package crypto

import (
	"bytes"
	"testing"
)

// ==================== DEK-Only Tests ====================

func TestDEKOnly_BasicEncryption(t *testing.T) {
	masterKey := make([]byte, 32)
	for i := range masterKey {
		masterKey[i] = byte(i)
	}

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		t.Fatalf("Cannot create CryptoCore: %v", err)
	}

	plaintext := []byte("Hello, this is a test message for DEK-only encryption!")

	// Test EncryptFile (should use envelope encryption)
	ciphertext, err := cc.EncryptFileEnvelope(plaintext, 1)
	if err != nil {
		t.Fatalf("EncryptFile failed: %v", err)
	}

	// Verify envelope header
	if len(ciphertext) < ENVELOPE_HEADER_SIZE {
		t.Fatalf("Ciphertext too short, expected at least %d bytes, got %d", ENVELOPE_HEADER_SIZE, len(ciphertext))
	}

	header, err := ParseEnvelopeFileHeader(ciphertext[:ENVELOPE_HEADER_SIZE])
	if err != nil {
		t.Fatalf("Cannot parse envelope header: %v", err)
	}

	if header.Version != 3 {
		t.Fatalf("Expected version 3 (DEK-only), got %d", header.Version)
	}

	// Test DecryptFile
	decrypted, err := cc.DecryptFileEnvelope(ciphertext)
	if err != nil {
		t.Fatalf("DecryptFile failed: %v", err)
	}

	if !bytes.Equal(plaintext, decrypted) {
		t.Fatalf("Decrypted data doesn't match original.\nOriginal: %s\nDecrypted: %s", plaintext, decrypted)
	}

	t.Logf("✅ DEK-only encryption test passed!")
	t.Logf("   Original size: %d bytes", len(plaintext))
	t.Logf("   Encrypted size: %d bytes", len(ciphertext))
	t.Logf("   Overhead: %d bytes", len(ciphertext)-len(plaintext))
}

func TestDEKOnly_EmptyFile(t *testing.T) {
	masterKey := make([]byte, 32)
	for i := range masterKey {
		masterKey[i] = byte(i + 100)
	}

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		t.Fatalf("Cannot create CryptoCore: %v", err)
	}

	plaintext := []byte{}

	ciphertext, err := cc.EncryptFileEnvelope(plaintext, 1)
	if err != nil {
		t.Fatalf("EncryptFile failed: %v", err)
	}

	// Empty file should only have header
	if len(ciphertext) != ENVELOPE_HEADER_SIZE {
		t.Fatalf("Empty file should only have header, expected %d bytes, got %d", ENVELOPE_HEADER_SIZE, len(ciphertext))
	}

	decrypted, err := cc.DecryptFileEnvelope(ciphertext)
	if err != nil {
		t.Fatalf("DecryptFile failed: %v", err)
	}

	if len(decrypted) != 0 {
		t.Fatalf("Expected empty result, got %d bytes", len(decrypted))
	}

	t.Logf("✅ DEK-only empty file test passed!")
}

func TestDEKOnly_LargeFile(t *testing.T) {
	masterKey := make([]byte, 32)
	for i := range masterKey {
		masterKey[i] = byte(i + 200)
	}

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		t.Fatalf("Cannot create CryptoCore: %v", err)
	}

	// Create large file (10KB)
	plaintext := make([]byte, 10240)
	for i := range plaintext {
		plaintext[i] = byte(i % 256)
	}

	ciphertext, err := cc.EncryptFileEnvelope(plaintext, 1)
	if err != nil {
		t.Fatalf("EncryptFile failed: %v", err)
	}

	decrypted, err := cc.DecryptFileEnvelope(ciphertext)
	if err != nil {
		t.Fatalf("DecryptFile failed: %v", err)
	}

	if !bytes.Equal(plaintext, decrypted) {
		t.Fatalf("Decrypted data doesn't match original")
	}

	t.Logf("✅ DEK-only large file test passed!")
	t.Logf("   Original size: %d bytes", len(plaintext))
	t.Logf("   Encrypted size: %d bytes", len(ciphertext))
	t.Logf("   Overhead: %d bytes", len(ciphertext)-len(plaintext))
}

func TestDEKOnly_FilenameEncryption(t *testing.T) {
	masterKey := make([]byte, 32)
	for i := range masterKey {
		masterKey[i] = byte(i + 50)
	}

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		t.Fatalf("Cannot create CryptoCore: %v", err)
	}

	plainName := "test-file.txt"
	encryptedName, err := cc.EncryptFilename(plainName, "./")
	if err != nil {
		t.Fatalf("EncryptFilename failed: %v", err)
	}

	decryptedName, err := cc.DecryptFilename(encryptedName, "./")
	if err != nil {
		t.Fatalf("DecryptFilename failed: %v", err)
	}

	if plainName != decryptedName {
		t.Fatalf("Decrypted filename doesn't match.\nOriginal: %s\nDecrypted: %s", plainName, decryptedName)
	}

	t.Logf("✅ DEK-only filename encryption test passed!")
	t.Logf("   Original: %s", plainName)
	t.Logf("   Encrypted: %s", encryptedName)
	t.Logf("   Decrypted: %s", decryptedName)
}

func TestDEKOnly_LegacyFormatRejection(t *testing.T) {
	masterKey := make([]byte, 32)
	for i := range masterKey {
		masterKey[i] = byte(i)
	}

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		t.Fatalf("Cannot create CryptoCore: %v", err)
	}

	// Test legacy file format rejection
	legacyData := make([]byte, FILE_HEADER_SIZE+100) // Fake legacy format
	_, err = cc.DecryptFileEnvelope(legacyData)
	if err == nil {
		t.Fatalf("DecryptFile should reject legacy format")
	}

	// Test with invalid envelope header
	invalidEnvelopeData := make([]byte, ENVELOPE_HEADER_SIZE-1) // Too short
	_, err = cc.DecryptFileEnvelope(invalidEnvelopeData)
	if err == nil {
		t.Fatalf("DecryptFile should reject invalid envelope data")
	}

	t.Logf("✅ Legacy format rejection test passed!")
}
