package main

import (
	"flag"
	"fmt"
	"gocryptfs/internal/config"
	"gocryptfs/internal/crypto"
	"gocryptfs/internal/fusefs"
	"os"
	"path/filepath"
)

const (
	// Version của gocryptfs DEK-only
	APP_VERSION = "v2.0.0-dek-only"
)

// Args chứa các command line arguments (DEK-only mode)
type Args struct {
	Init       bool
	Debug      bool
	Version    bool
	Quiet      bool
	Rotate     bool // Rotate master key
	Migrate    bool // Migrate to envelope encryption
	CipherDir  string
	MountPoint string
	Password   string
	// NewPassword string // New password for key rotation
}

func main() {
	// newConf, err := config.LoadConfig("/Users/<USER>/Documents/Learn/private/go/gocryptfs/demo/cipher/gocryptfs.conf")
	// if err != nil {
	// 	return
	// }
	// // Verify password và lấy master key
	// newMasterKey, err := newConf.DecryptMasterKey("123456")
	// if err != nil {
	// 	return
	// }
	// // FS
	// newFS, err := fusefs.NewFSWithConfig("/Users/<USER>/Documents/Learn/private/go/gocryptfs/demo/cipher/gocryptfs.conf", newMasterKey, newConf, false)
	// if err != nil {
	// 	return
	// }
	// cipherName, err := newFS.CryptoCore.EncryptFilename("counter.txt", "/Users/<USER>/Documents/Learn/private/go/gocryptfs/demo/cipher")
	// if err != nil {
	// 	return
	// }
	// rawName, err := newFS.CryptoCore.DecryptFilename(cipherName, "/Users/<USER>/Documents/Learn/private/go/gocryptfs/demo/cipher")
	// if err != nil {
	// 	return
	// }
	// fmt.Println(cipherName, rawName)
	// return

	var args Args

	// Parse command line flags
	flag.BoolVar(&args.Init, "init", false, "Initialize encrypted directory (DEK-only)")
	flag.BoolVar(&args.Debug, "debug", false, "Enable debug output")
	flag.BoolVar(&args.Version, "version", false, "Print version and exit")
	flag.BoolVar(&args.Quiet, "quiet", false, "Quiet operation")
	flag.BoolVar(&args.Rotate, "rotate", false, "Rotate master key (DEK-only)")
	flag.BoolVar(&args.Migrate, "migrate", false, "Migrate to envelope encryption (DEK-only)")
	// flag.StringVar(&args.NewPassword, "new-password", "", "New password for key rotation")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "gocryptfs %s - DEK-only Encrypted overlay filesystem\n\n", APP_VERSION)
		fmt.Fprintf(os.Stderr, "Usage:\n")
		fmt.Fprintf(os.Stderr, "  Initialize: gocryptfs -init CIPHERDIR\n")
		fmt.Fprintf(os.Stderr, "  Mount:      gocryptfs CIPHERDIR MOUNTPOINT\n")
		fmt.Fprintf(os.Stderr, "  Rotate:     gocryptfs -rotate CIPHERDIR\n\n")
		fmt.Fprintf(os.Stderr, "Options:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\nNote: This version only supports DEK (Data Encryption Key) mode.\n")
		fmt.Fprintf(os.Stderr, "Legacy encryption formats are not supported.\n")
	}

	flag.Parse()

	// Handle version flag
	if args.Version {
		fmt.Printf("gocryptfs %s\n", APP_VERSION)
		os.Exit(0)
	}

	// Parse positional arguments
	switch flag.NArg() {
	case 1:
		if !args.Init && !args.Rotate && !args.Migrate {
			fmt.Fprintf(os.Stderr, "Error: Missing mountpoint\n")
			flag.Usage()
			os.Exit(1)
		}
		args.CipherDir = flag.Arg(0)
	case 2:
		if args.Init {
			fmt.Fprintf(os.Stderr, "Error: Too many arguments for -init\n")
			flag.Usage()
			os.Exit(1)
		}
		args.CipherDir = flag.Arg(0)
		args.MountPoint = flag.Arg(1)
	default:
		fmt.Fprintf(os.Stderr, "Error: Wrong number of arguments\n")
		flag.Usage()
		os.Exit(1)
	}

	// Validate paths
	if err := validatePaths(&args); err != nil {
		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		os.Exit(1)
	}

	if args.Init {
		if err := initDir(&args); err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
	} else if args.Rotate {
		if err := rotateKeys(&args); err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
		// } else if args.Migrate {
		// 	if err := migrateRotate(&args); err != nil {
		// 		fmt.Fprintf(os.Stderr, "Error: %v\n", err)
		// 		os.Exit(1)
		// 	}
	} else {
		if err := mountFs(&args); err != nil {
			fmt.Fprintf(os.Stderr, "Error: %v\n", err)
			os.Exit(1)
		}
	}
}

// validatePaths kiểm tra tính hợp lệ của các đường dẫn
func validatePaths(args *Args) error {
	// Chuyển đổi sang absolute path
	absPath, err := filepath.Abs(args.CipherDir)
	if err != nil {
		return fmt.Errorf("invalid cipher directory: %v", err)
	}
	args.CipherDir = absPath

	if !args.Init {
		absMount, err := filepath.Abs(args.MountPoint)
		if err != nil {
			return fmt.Errorf("invalid mount point: %v", err)
		}
		args.MountPoint = absMount

		// Kiểm tra mountpoint tồn tại và là directory
		stat, err := os.Stat(args.MountPoint)
		if err != nil {
			return fmt.Errorf("mount point does not exist: %v", err)
		}
		if !stat.IsDir() {
			return fmt.Errorf("mount point is not a directory")
		}
	}

	return nil
}

// initDir khởi tạo một encrypted directory mới
func initDir(args *Args) error {
	fmt.Printf("Initializing encrypted directory at %s\n", args.CipherDir)

	// Tạo directory nếu chưa tồn tại
	if err := os.MkdirAll(args.CipherDir, 0755); err != nil {
		return fmt.Errorf("cannot create directory: %v", err)
	}

	// Kiểm tra directory có trống không
	entries, err := os.ReadDir(args.CipherDir)
	if err != nil {
		return fmt.Errorf("cannot read directory: %v", err)
	}
	if len(entries) > 0 {
		return fmt.Errorf("directory is not empty")
	}

	// Prompt cho password
	password, err := readPassword("Enter password: ")
	if err != nil {
		return fmt.Errorf("cannot read password: %v", err)
	}

	// Tạo config với DEK-only mode
	conf := config.NewConfig()
	fmt.Printf("Using DEK-only encryption mode (version 3)\n")

	configPath := filepath.Join(args.CipherDir, crypto.CONFIG_FILENAME)

	// Write config file
	if err := conf.WriteConfig(configPath, password); err != nil {
		return fmt.Errorf("cannot write config: %v", err)
	}

	fmt.Printf("Filesystem initialized successfully.\n")
	fmt.Printf("Config file: %s\n", configPath)

	return nil
}

// mountFs mount encrypted filesystem
func mountFs(args *Args) error {
	configPath := filepath.Join(args.CipherDir, crypto.CONFIG_FILENAME)

	// Đọc config
	conf, err := config.LoadConfig(configPath)
	if err != nil {
		return fmt.Errorf("cannot load config: %v", err)
	}

	// Prompt cho password
	password, err := readPassword("Enter password: ")
	if err != nil {
		return fmt.Errorf("cannot read password: %v", err)
	}

	// Verify password và lấy master key
	masterKey, err := conf.DecryptMasterKey(password)
	if err != nil {
		return fmt.Errorf("wrong password or corrupted config file")
	}

	fmt.Printf("Master key length: %d bytes\n", len(masterKey))
	fmt.Printf("Mounting %s at %s\n", args.CipherDir, args.MountPoint)

	// Mount filesystem với config
	fs, err := fusefs.NewFSWithConfig(args.CipherDir, masterKey, conf, args.Debug)
	if err != nil {
		return fmt.Errorf("cannot create filesystem: %v", err)
	}

	if err := fs.Mount(args.MountPoint); err != nil {
		return fmt.Errorf("mount failed: %v", err)
	}

	return nil
}

// rotateKeys rotate master key cho envelope encrypted filesystem
func rotateKeys(args *Args) error {
	configPath := filepath.Join(args.CipherDir, crypto.CONFIG_FILENAME)

	// ========== old ==========

	// Đọc config
	oldConf, err := config.LoadConfig(configPath)
	if err != nil {
		return fmt.Errorf("cannot load config: %v", err)
	}

	fmt.Printf("Rotating master key for %s\n", args.CipherDir)
	fmt.Printf("Current master key version: %d\n", oldConf.MasterKeyVersion)

	// ========== new ==========

	// Prompt cho password mới
	newPassword, err := readPassword("Enter new password: ")
	if err != nil {
		return fmt.Errorf("cannot read password: %v", err)
	}

	// Tạo master key mới bằng cách tạo config mới với password mới
	newConf := config.NewConfig()
	newConf.SetMasterKeyVersion(oldConf.MasterKeyVersion + 1)

	// Tạo temp file để generate master key mới
	tempConfigPath := configPath + ".temp"
	err = newConf.WriteConfig(tempConfigPath, newPassword)
	if err != nil {
		return fmt.Errorf("cannot create temp config: %v", err)
	}
	defer os.Remove(tempConfigPath)

	// Rotate keys cho tất cả files trong directory
	fmt.Printf("Rotating to master key version: %d\n", newConf.MasterKeyVersion)

	// ========== create ==========

	// Backup old config
	config.BackupConfig(args.CipherDir, oldConf.MasterKeyVersion)

	// Update config với master key mới
	err = newConf.WriteConfig(configPath, newPassword)
	if err != nil {
		return fmt.Errorf("cannot update config: %v", err)
	}

	// ========== migrate ==========

	{
		// newFS sẽ là filesystem với master key mới
		newConfig, err := config.LoadConfig(configPath)
		if err != nil {
			return err
		}
		masterKey, err := newConfig.DecryptMasterKey(newPassword)
		if err != nil {
			return err
		}
		// Mount filesystem với config
		newFS, err := fusefs.NewFSWithConfig(args.CipherDir, masterKey, newConfig, args.Debug)
		if err != nil {
			return err
		}

		fusefs.MigrateOldDatas(newFS, args.CipherDir)
	}

	fmt.Printf("Key rotation completed successfully!\n")
	fmt.Printf("New master key version: %d\n", newConf.MasterKeyVersion)
	fmt.Printf("All envelope encrypted files have been rotated.\n")
	fmt.Printf("Backup files created with .backup.YYYYMMDD-HHMMSS extension.\n")

	return nil
}

// readPassword đọc password từ terminal
func readPassword(prompt string) (string, error) {
	fmt.Print(prompt)

	// TODO: Implement proper password hiding
	// For now, just read normally (password will be visible)
	var password string
	fmt.Scanln(&password)

	return password, nil
}

// // migrateRotate migrate files to new master key version
// func migrateRotate(args *Args) error {
// 	configPath := filepath.Join(args.CipherDir, crypto.CONFIG_FILENAME)

// 	// Đọc config
// 	conf, err := config.LoadConfig(configPath)
// 	if err != nil {
// 		return fmt.Errorf("cannot load config: %v", err)
// 	}

// 	// Prompt cho password
// 	// readPassword("Enter password: ")
// 	password, err := "123456", nil
// 	if err != nil {
// 		return fmt.Errorf("cannot read password: %v", err)
// 	}

// 	// Verify password và lấy master key
// 	masterKey, err := conf.DecryptMasterKey(password)
// 	if err != nil {
// 		return fmt.Errorf("wrong password or corrupted config file")
// 	}

// 	fmt.Printf("Starting migration for %s\n", args.CipherDir)
// 	fmt.Printf("Current master key version: %d\n", conf.GetMasterKeyVersion())

// 	// Tạo crypto core
// 	cc, err := crypto.NewCryptoCore(masterKey)
// 	if err != nil {
// 		return fmt.Errorf("cannot create crypto core: %v", err)
// 	}

// 	// Tạo migration service với master key hiện tại
// 	// Migration sẽ migrate từ version cũ lên version hiện tại
// 	currentVersion := conf.GetMasterKeyVersion()
// 	mig := fusefs.NewMigrationService(cc, args.CipherDir, masterKey, currentVersion)
// 	if mig == nil {
// 		return fmt.Errorf("cannot create migration service")
// 	}

// 	// Bắt đầu migration
// 	fmt.Printf("Starting migration to master key version %d...\n", currentVersion)
// 	err = mig.Start()
// 	if err != nil {
// 		return fmt.Errorf("migration failed: %v", err)
// 	}

// 	// Chờ migration hoàn thành
// 	fmt.Printf("Migration started. Monitoring progress...\n")
// 	for mig.IsRunning() {
// 		total, migrated, failed, current := mig.GetProgress()
// 		fmt.Printf("Progress: %d/%d migrated, %d failed, current: %s\n",
// 			migrated, total, failed, current)

// 		// Sleep 1 giây trước khi check lại
// 		time.Sleep(1 * time.Second)
// 	}

// 	// In kết quả cuối cùng
// 	total, migrated, failed, _ := mig.GetProgress()
// 	fmt.Printf("\nMigration completed!\n")
// 	fmt.Printf("Total files: %d\n", total)
// 	fmt.Printf("Successfully migrated: %d\n", migrated)
// 	fmt.Printf("Failed: %d\n", failed)

// 	if failed > 0 {
// 		fmt.Printf("Warning: %d files failed to migrate. Check logs for details.\n", failed)
// 	}

// 	return nil
// }
