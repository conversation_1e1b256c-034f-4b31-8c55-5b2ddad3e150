package config

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/json"
	"fmt"
	"gocryptfs/internal/crypto"
	"os"

	"golang.org/x/crypto/scrypt"
)

const (
	// SCRYPT_N là cost parameter cho scrypt
	SCRYPT_N = 65536
	// SCRYPT_R là block size parameter cho scrypt
	SCRYPT_R = 8
	// SCRYPT_P là parallelization parameter cho scrypt
	SCRYPT_P = 1
	// SCRYPT_KEY_LEN là độ dài key được tạo bởi scrypt
	SCRYPT_KEY_LEN = 32

	// MASTER_KEY_LEN là độ dài master key
	MASTER_KEY_LEN = 32
	// SALT_LEN là độ dài salt
	SALT_LEN = 32
)

// // FeatureFlags chứa các feature flags của filesystem
// // Chỉ hỗ trợ DEK-based encryption, loại bỏ legacy features
// type FeatureFlags struct {
// 	// DirIV: mỗi directory có IV riêng (required for DEK)
// 	DirIV bool `json:"DirIV"`
// 	// LongNames: hỗ trợ tên file dài (required for DEK)
// 	LongNames bool `json:"LongNames"`
// 	// EnvelopeEncryption: sử dụng envelope encryption (always true for DEK)
// 	EnvelopeEncryption bool `json:"EnvelopeEncryption"`
// }

// Config chứa cấu hình của filesystem
type Config struct {
	// // Creator là tên và version của tool tạo ra filesystem
	// Creator string `json:"Creator"`
	// EncryptedKey là master key được mã hóa
	EncryptedKey []byte `json:"EncryptedKey"`
	// ScryptObject chứa parameters cho scrypt
	ScryptObject ScryptKdf `json:"ScryptObject"`
	// Version là version của config format
	Version int `json:"Version"`
	// FeatureFlags chứa các feature flags
	FeatureFlags []string `json:"FeatureFlags"`
	// MasterKeyVersion cho envelope encryption key rotation
	MasterKeyVersion uint16 `json:"MasterKeyVersion,omitempty"`
}

// ScryptKdf chứa parameters cho scrypt key derivation
type ScryptKdf struct {
	Salt   []byte `json:"Salt"`
	N      int    `json:"N"`
	R      int    `json:"R"`
	P      int    `json:"P"`
	KeyLen int    `json:"KeyLen"`
}

// NewConfig tạo một config mới với DEK-only settings
func NewConfig() *Config {
	return &Config{
		// Creator: "gocryptfs-dek v2.0.0",
		Version: 3, // Version 3 for DEK-only
		FeatureFlags: []string{
			"DirIV",
			"LongNames",
			"EnvelopeEncryption",
		},
		MasterKeyVersion: 1, // Default master key version
	}
}

// LoadConfig đọc config từ file
func LoadConfig(configPath string) (*Config, error) {
	data, err := os.ReadFile(configPath)
	if err != nil {
		return nil, fmt.Errorf("cannot read config file: %v", err)
	}

	var config Config
	if err := json.Unmarshal(data, &config); err != nil {
		return nil, fmt.Errorf("cannot parse config file: %v", err)
	}

	return &config, nil
}

// WriteConfig ghi config ra file, mã hóa master key bằng password
func (c *Config) WriteConfig(configPath, password string) error {

	// // nếu exist thì return error
	// if _, err := os.Stat(configPath); !os.IsNotExist(err) {
	// 	return fmt.Errorf("config file already exists: %s", configPath)
	// }

	// Tạo random master key
	masterKey := make([]byte, MASTER_KEY_LEN)
	if _, err := rand.Read(masterKey); err != nil {
		return fmt.Errorf("cannot generate master key: %v", err)
	}

	// Tạo random salt cho scrypt
	salt := make([]byte, SALT_LEN)
	if _, err := rand.Read(salt); err != nil {
		return fmt.Errorf("cannot generate salt: %v", err)
	}

	// Derive key từ password bằng scrypt
	derivedKey, err := scrypt.Key([]byte(password), salt, SCRYPT_N, SCRYPT_R, SCRYPT_P, SCRYPT_KEY_LEN)
	if err != nil {
		return fmt.Errorf("scrypt failed: %v", err)
	}

	// Mã hóa master key bằng derived key (đơn giản XOR cho demo)
	encryptedKey := make([]byte, len(masterKey))
	for i := range masterKey {
		encryptedKey[i] = masterKey[i] ^ derivedKey[i]
	}

	// Set config fields
	c.EncryptedKey = encryptedKey
	c.ScryptObject = ScryptKdf{
		Salt:   salt,
		N:      SCRYPT_N,
		R:      SCRYPT_R,
		P:      SCRYPT_P,
		KeyLen: SCRYPT_KEY_LEN,
	}

	// Serialize to JSON
	jsonData, err := json.MarshalIndent(c, "", "  ")
	if err != nil {
		return fmt.Errorf("cannot marshal config: %v", err)
	}

	// Write to file
	if err := os.WriteFile(configPath, jsonData, 0600); err != nil {
		return fmt.Errorf("cannot write config file: %v", err)
	}

	return nil
}

// Backup old config
func BackupConfig(cipherDir string, version uint16) (string, error) {
	configPath := cipherDir + "/" + crypto.CONFIG_FILENAME
	backupPath := fmt.Sprintf("%v/_backup.%d.%v", cipherDir, version, crypto.CONFIG_FILENAME)

	data, err := os.ReadFile(configPath)
	if err != nil {
		return "", fmt.Errorf("cannot read config file: %v", err)
	}

	// create file _backup.
	if err := os.WriteFile(backupPath, data, 0600); err != nil {
		return "", fmt.Errorf("cannot write config file: %v", err)
	}

	return backupPath, nil
}

// SetMasterKeyVersion thiết lập master key version
func (c *Config) SetMasterKeyVersion(version uint16) {
	c.MasterKeyVersion = version
}

// DecryptMasterKey giải mã master key bằng password
func (c *Config) DecryptMasterKey(password string) ([]byte, error) {
	// Derive key từ password bằng scrypt với parameters từ config
	derivedKey, err := scrypt.Key([]byte(password), c.ScryptObject.Salt,
		c.ScryptObject.N, c.ScryptObject.R, c.ScryptObject.P, c.ScryptObject.KeyLen)
	if err != nil {
		return nil, fmt.Errorf("scrypt failed: %v", err)
	}

	// Giải mã master key (đơn giản XOR cho demo)
	masterKey := make([]byte, len(c.EncryptedKey))
	for i := range c.EncryptedKey {
		masterKey[i] = c.EncryptedKey[i] ^ derivedKey[i]
	}

	// TODO: Verify master key bằng cách nào đó (ví dụ: checksum)
	// Hiện tại chỉ kiểm tra độ dài
	if len(masterKey) != MASTER_KEY_LEN {
		return nil, fmt.Errorf("invalid master key length")
	}

	return masterKey, nil
}

// =================================================================

// ValidateConfig kiểm tra tính hợp lệ của config (DEK-only)
func (c *Config) ValidateConfig() error {
	// Chỉ chấp nhận version 3 (DEK-only)
	if c.Version != 3 {
		return fmt.Errorf("unsupported config version: %d, only version 3 (DEK-only) is supported", c.Version)
	}

	if len(c.EncryptedKey) != MASTER_KEY_LEN {
		return fmt.Errorf("invalid encrypted key length: %d", len(c.EncryptedKey))
	}

	if len(c.ScryptObject.Salt) != SALT_LEN {
		return fmt.Errorf("invalid salt length: %d", len(c.ScryptObject.Salt))
	}

	if c.ScryptObject.KeyLen != SCRYPT_KEY_LEN {
		return fmt.Errorf("invalid scrypt key length: %d", c.ScryptObject.KeyLen)
	}

	// // Kiểm tra EnvelopeEncryption flag phải có
	// flags := c.GetFeatureFlags()
	// if !flags.EnvelopeEncryption {
	// 	return fmt.Errorf("EnvelopeEncryption flag is required for DEK-only mode")
	// }

	return nil
}

// CreateMasterKeyChecksum tạo checksum cho master key để verify
func CreateMasterKeyChecksum(masterKey []byte) []byte {
	hash := sha256.Sum256(masterKey)
	return hash[:16] // Chỉ lấy 16 bytes đầu
}

// // IsEnvelopeEncryptionEnabled kiểm tra xem envelope encryption có được bật không
// func (c *Config) IsEnvelopeEncryptionEnabled() bool {
// 	flags := c.GetFeatureFlags()
// 	return flags.EnvelopeEncryption
// }

// // GetFeatureFlags trả về FeatureFlags struct từ string slice (DEK-only)
// func (c *Config) GetFeatureFlags() FeatureFlags {
// 	var flags FeatureFlags

// 	for _, flag := range c.FeatureFlags {
// 		switch flag {
// 		case "DirIV":
// 			flags.DirIV = true
// 		case "LongNames":
// 			flags.LongNames = true
// 		case "EnvelopeEncryption":
// 			flags.EnvelopeEncryption = true
// 		}
// 	}

// 	// Đảm bảo EnvelopeEncryption luôn được bật cho DEK-only mode
// 	flags.EnvelopeEncryption = true

// 	return flags
// }
