#!/bin/bash

# gocryptfs UI Starter Script
# Starts both backend API server and React frontend

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to check if port is in use
port_in_use() {
    lsof -i :$1 >/dev/null 2>&1
}

# Function to kill process on port
kill_port() {
    local port=$1
    local pid=$(lsof -ti :$port)
    if [ ! -z "$pid" ]; then
        print_warning "Killing process on port $port (PID: $pid)"
        kill -9 $pid 2>/dev/null || true
        sleep 1
    fi
}

# Function to cleanup on exit
cleanup() {
    print_step "Cleaning up..."
    kill_port 3000  # React dev server
    kill_port 3001  # Backend API server
    exit 0
}

# Set trap for cleanup
trap cleanup SIGINT SIGTERM

# Check prerequisites
print_step "Checking prerequisites..."

if ! command_exists node; then
    print_error "Node.js is not installed. Please install Node.js first."
    exit 1
fi

if ! command_exists npm; then
    print_error "npm is not installed. Please install npm first."
    exit 1
fi

# Check if gocryptfs binary exists
GOCRYPTFS_BINARY="../build/gocryptfs"
if [ ! -f "$GOCRYPTFS_BINARY" ]; then
    print_warning "gocryptfs binary not found at $GOCRYPTFS_BINARY"
    print_step "Building gocryptfs binary..."
    cd ..
    make build
    cd ui
    if [ ! -f "$GOCRYPTFS_BINARY" ]; then
        print_error "Failed to build gocryptfs binary"
        exit 1
    fi
fi

print_success "Prerequisites check passed"

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_step "Installing frontend dependencies..."
    npm install
fi

if [ ! -d "backend/node_modules" ]; then
    print_step "Installing backend dependencies..."
    cd backend
    npm install
    cd ..
fi

# Kill any existing processes on our ports
if port_in_use 3000; then
    kill_port 3000
fi

if port_in_use 3001; then
    kill_port 3001
fi

# Start backend server
print_step "Starting backend API server on port 3001..."
cd backend
npm start &
BACKEND_PID=$!
cd ..

# Wait for backend to start
sleep 3

# Check if backend started successfully
if ! port_in_use 3001; then
    print_error "Backend server failed to start"
    kill $BACKEND_PID 2>/dev/null || true
    exit 1
fi

print_success "Backend API server started (PID: $BACKEND_PID)"

# Start frontend
print_step "Starting React frontend on port 3000..."
npm start &
FRONTEND_PID=$!

# Wait for frontend to start
sleep 5

# Check if frontend started successfully
if ! port_in_use 3000; then
    print_error "Frontend server failed to start"
    kill $BACKEND_PID 2>/dev/null || true
    kill $FRONTEND_PID 2>/dev/null || true
    exit 1
fi

print_success "React frontend started (PID: $FRONTEND_PID)"

print_success "gocryptfs UI is now running!"
echo ""
print_step "Access the UI at: http://localhost:3000"
print_step "Backend API at: http://localhost:3001"
echo ""
print_step "Press Ctrl+C to stop both servers"

# Wait for user to stop
wait
