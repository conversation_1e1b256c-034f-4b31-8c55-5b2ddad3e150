#!/bin/bash

# gocryptfs Demo Script
# Comprehensive demo and management script for gocryptfs

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}[STEP]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
gocryptfs Demo Script - Comprehensive Management Tool

Usage: $0 [command] [options]

Commands:
    setup       Setup demo environment và build binary
    init        Initialize encrypted filesystem
    mount       Mount filesystem và demo operations
    dek-demo    Run DEK-only demonstration
    test        Run comprehensive tests
    ui          Start web UI interface
    clean       Clean up demo files
    help        Show this help message

Options:
    --keep      Keep filesystem mounted after demo (for dek-demo)
    --verbose   Enable verbose output (for test)
    --coverage  Generate coverage report (for test)

Examples:
    $0 setup           # Build binary và setup demo directories
    $0 init            # Initialize cipher directory với password
    $0 mount           # Mount filesystem và demo file operations
    $0 dek-demo        # Run DEK-only demonstration
    $0 dek-demo --keep # Run DEK-only demo and keep mounted
    $0 test            # Run test suite
    $0 test --verbose  # Run tests with verbose output
    $0 ui              # Start web UI interface
    $0 clean           # Clean up all demo files
EOF
}

# Setup demo environment
setup_demo() {
    print_step "Setting up gocryptfs demo environment..."
    
    # Build binary
    print_step "Building gocryptfs binary..."
    make build
    
    # Create demo directories
    print_step "Creating demo directories..."
    mkdir -p demo/cipher demo/plain
    
    print_success "Demo environment setup completed!"
    echo "Demo directories created:"
    echo "  - demo/cipher (encrypted files storage)"
    echo "  - demo/plain (mount point)"
    echo "  - build/gocryptfs (binary)"
}

# Initialize encrypted filesystem
init_filesystem() {
    print_step "Initializing encrypted filesystem..."
    
    if [ ! -f "build/gocryptfs" ]; then
        print_error "Binary not found. Run '$0 setup' first."
        exit 1
    fi
    
    if [ ! -d "demo/cipher" ]; then
        print_error "Demo directories not found. Run '$0 setup' first."
        exit 1
    fi
    
    print_step "Initializing cipher directory..."
    echo "Please enter a password for the encrypted filesystem:"
    ./build/gocryptfs -init demo/cipher
    
    print_success "Encrypted filesystem initialized!"
    echo "Configuration file created: demo/cipher/gocryptfs.conf"
}

# Mount filesystem and demo operations
mount_and_demo() {
    print_step "Mounting filesystem và demonstrating operations..."
    
    if [ ! -f "demo/cipher/gocryptfs.conf" ]; then
        print_error "Encrypted filesystem not initialized. Run '$0 init' first."
        exit 1
    fi
    
    # Check if already mounted
    if mount | grep -q "demo/plain"; then
        print_warning "Filesystem already mounted. Unmounting first..."
        umount demo/plain 2>/dev/null || true
        sleep 1
    fi
    
    print_step "Mounting encrypted filesystem..."
    echo "Please enter your password:"
    
    # Mount in background
    ./build/gocryptfs demo/cipher demo/plain &
    MOUNT_PID=$!
    
    # Wait a bit for mount to complete
    sleep 2
    
    # Check if mount was successful
    if ! mount | grep -q "demo/plain"; then
        print_error "Mount failed!"
        kill $MOUNT_PID 2>/dev/null || true
        exit 1
    fi
    
    print_success "Filesystem mounted successfully!"
    
    # Demo file operations
    print_step "Demonstrating file operations..."
    
    # Create test files
    echo "Hello, World!" > demo/plain/test.txt
    echo "This is a secret document" > demo/plain/secret.txt
    mkdir -p demo/plain/documents
    echo "Document in subdirectory" > demo/plain/documents/doc1.txt
    
    # Show plain files
    print_step "Files in plain directory:"
    ls -la demo/plain/
    echo ""
    ls -la demo/plain/documents/
    
    # Show encrypted files
    print_step "Encrypted files in cipher directory:"
    ls -la demo/cipher/
    
    # Show file contents
    print_step "Content of plain files:"
    echo "=== test.txt ==="
    cat demo/plain/test.txt
    echo ""
    echo "=== secret.txt ==="
    cat demo/plain/secret.txt
    echo ""
    echo "=== documents/doc1.txt ==="
    cat demo/plain/documents/doc1.txt
    echo ""
    
    print_step "Encrypted file contents (should be unreadable):"
    echo "=== First encrypted file ==="
    encrypted_file=$(ls demo/cipher/ | grep -v gocryptfs | head -1)
    if [ -n "$encrypted_file" ]; then
        echo "File: $encrypted_file"
        hexdump -C "demo/cipher/$encrypted_file" | head -5
        echo "... (truncated)"
    fi
    
    print_success "Demo completed!"
    print_warning "Filesystem is still mounted at demo/plain"
    print_warning "To unmount: umount demo/plain"
    print_warning "Or press Ctrl+C to stop the gocryptfs process"
    
    # Wait for user input
    echo ""
    echo "Press Enter to unmount filesystem..."
    read
    
    # Unmount
    print_step "Unmounting filesystem..."
    umount demo/plain
    kill $MOUNT_PID 2>/dev/null || true
    
    print_success "Filesystem unmounted successfully!"
}

# Run DEK-only demonstration
run_dek_demo() {
    local keep_mounted=false
    if [ "$1" = "--keep" ]; then
        keep_mounted=true
    fi

    print_step "Running DEK-only demonstration..."

    # Setup temporary directories
    TEMP_DIR="/tmp/gocryptfs_dek_demo_$$"
    CIPHER_DIR="$TEMP_DIR/cipher"
    MOUNT_DIR="$TEMP_DIR/mount"

    # Cleanup function
    cleanup_dek() {
        print_step "Cleaning up DEK demo..."
        if mountpoint -q "$MOUNT_DIR" 2>/dev/null; then
            fusermount -u "$MOUNT_DIR" 2>/dev/null || umount "$MOUNT_DIR" 2>/dev/null || true
        fi
        rm -rf "$TEMP_DIR"
        print_success "DEK demo cleanup completed"
    }

    # Set trap for cleanup
    if [ "$keep_mounted" = false ]; then
        trap cleanup_dek EXIT
    fi

    mkdir -p "$TEMP_DIR" "$MOUNT_DIR"

    print_step "Demo directories:"
    echo "  Cipher: $CIPHER_DIR"
    echo "  Mount: $MOUNT_DIR"

    # Build if needed
    if [ ! -f "build/gocryptfs" ]; then
        print_step "Building gocryptfs..."
        make build
    fi

    # Initialize DEK-only filesystem
    print_step "Initializing DEK-only filesystem..."
    echo "123456" | ./build/gocryptfs -init "$CIPHER_DIR"
    print_success "DEK-only filesystem initialized"

    # Mount filesystem
    print_step "Mounting filesystem..."
    echo "123456" | ./build/gocryptfs "$CIPHER_DIR" "$MOUNT_DIR" &
    MOUNT_PID=$!
    sleep 2

    if ! mountpoint -q "$MOUNT_DIR"; then
        print_error "Mount failed!"
        kill $MOUNT_PID 2>/dev/null || true
        exit 1
    fi
    print_success "Filesystem mounted successfully"

    # Create test files
    print_step "Creating test files..."
    echo "Hello, DEK-only world!" > "$MOUNT_DIR/hello.txt"
    echo '{"message": "DEK encryption demo"}' > "$MOUNT_DIR/config.json"
    mkdir -p "$MOUNT_DIR/subdir"
    echo "Nested file content" > "$MOUNT_DIR/subdir/nested.txt"

    # Show results
    print_step "Plain files:"
    ls -la "$MOUNT_DIR"

    print_step "Encrypted files:"
    ls -la "$CIPHER_DIR"

    print_step "File contents:"
    echo "=== hello.txt ==="
    cat "$MOUNT_DIR/hello.txt"
    echo "=== config.json ==="
    cat "$MOUNT_DIR/config.json"

    print_success "DEK-only demonstration completed!"

    if [ "$keep_mounted" = true ]; then
        print_warning "Filesystem kept mounted at: $MOUNT_DIR"
        print_warning "Cipher directory: $CIPHER_DIR"
        print_warning "Press Ctrl+C to cleanup and exit"
        trap cleanup_dek SIGINT SIGTERM
        while true; do sleep 1; done
    fi
}

# Run tests
run_tests() {
    local test_args=""
    shift # Remove 'test' command

    # Parse test arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --verbose)
                test_args="$test_args --verbose"
                shift
                ;;
            --coverage)
                test_args="$test_args --coverage"
                shift
                ;;
            *)
                test_args="$test_args $1"
                shift
                ;;
        esac
    done

    print_step "Running gocryptfs test suite..."

    if [ -f "tests/run_tests.sh" ]; then
        ./tests/run_tests.sh $test_args
    else
        print_step "Running basic tests..."
        make test-unit
        make test-integration
        make test-security
        print_success "Basic tests completed!"
    fi
}

# Start UI interface
start_ui() {
    print_step "Starting gocryptfs web UI..."

    if [ -f "ui/start-ui.sh" ]; then
        cd ui
        ./start-ui.sh
        cd ..
    else
        print_error "UI script not found at ui/start-ui.sh"
        exit 1
    fi
}

# Clean up demo files
clean_demo() {
    print_step "Cleaning up demo files..."
    
    # Unmount if mounted
    if mount | grep -q "demo/plain"; then
        print_step "Unmounting filesystem..."
        umount demo/plain 2>/dev/null || true
    fi
    
    # Remove demo directories
    rm -rf demo/
    
    # Remove build artifacts
    rm -rf build/
    
    # Remove test artifacts
    rm -f coverage.out coverage.html
    rm -f *.prof
    
    print_success "Demo cleanup completed!"
}

# Main execution
case "${1:-help}" in
    setup)
        setup_demo
        ;;
    init)
        init_filesystem
        ;;
    mount)
        mount_and_demo
        ;;
    dek-demo)
        run_dek_demo "$2"
        ;;
    test)
        run_tests "$@"
        ;;
    ui)
        start_ui
        ;;
    clean)
        clean_demo
        ;;
    help|--help|-h)
        show_usage
        ;;
    *)
        print_error "Unknown command: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac

# Show next steps
if [ "$1" = "setup" ]; then
    echo ""
    print_step "Next steps:"
    echo "  1. Run '$0 init' to initialize encrypted filesystem"
    echo "  2. Run '$0 mount' to mount và demo file operations"
    echo "  3. Run '$0 dek-demo' to see DEK-only encryption"
    echo "  4. Run '$0 test' to run test suite"
    echo "  5. Run '$0 ui' to start web interface"
    echo "  6. Run '$0 clean' to clean up when done"
elif [ "$1" = "init" ]; then
    echo ""
    print_step "Next steps:"
    echo "  1. Run '$0 mount' to mount và demo file operations"
    echo "  2. Run '$0 dek-demo' to see DEK-only encryption"
    echo "  3. Run '$0 test' to run test suite"
elif [ "$1" = "mount" ]; then
    echo ""
    print_step "What happened:"
    echo "  - Files were created in demo/plain/"
    echo "  - They were automatically encrypted in demo/cipher/"
    echo "  - You can see the encrypted versions are unreadable"
    echo "  - The filesystem provides transparent encryption/decryption"
elif [ "$1" = "dek-demo" ]; then
    echo ""
    print_step "DEK-only features demonstrated:"
    echo "  - Each file encrypted with unique Data Encryption Key (DEK)"
    echo "  - Master key only encrypts DEKs, not file content directly"
    echo "  - Enhanced security through envelope encryption"
    echo "  - Filename and directory encryption"
fi
