package crypto

import (
	"crypto/rand"
	"testing"
)

// ==================== DEK-Only Benchmarks ====================

// BenchmarkDEKOnly_Creation đo performance tạo CryptoCore
func BenchmarkDEKOnly_Creation(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := NewCryptoCore(masterKey, 1)
		if err != nil {
			b.Fatalf("Cannot create crypto core: %v", err)
		}
	}
}

// BenchmarkDEKOnly_SmallFiles đo performance mã hóa files nhỏ (1KB)
func BenchmarkDEKOnly_SmallFiles(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	plaintext := make([]byte, 1024)
	rand.Read(plaintext)

	b.Run("Encrypt", func(b *testing.B) {
		b.ResetTimer()
		b.SetBytes(1024)
		for i := 0; i < b.N; i++ {
			_, err := cc.EncryptFileEnvelope(plaintext, 1)
			if err != nil {
				b.Fatalf("Encryption failed: %v", err)
			}
		}
	})

	// Prepare encrypted data for decryption benchmark
	ciphertext, _ := cc.EncryptFileEnvelope(plaintext, 1)

	b.Run("Decrypt", func(b *testing.B) {
		b.ResetTimer()
		b.SetBytes(1024)
		for i := 0; i < b.N; i++ {
			_, err := cc.DecryptFileEnvelope(ciphertext)
			if err != nil {
				b.Fatalf("Decryption failed: %v", err)
			}
		}
	})
}

// BenchmarkDEKOnly_MediumFiles đo performance mã hóa files trung bình (1MB)
func BenchmarkDEKOnly_MediumFiles(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	plaintext := make([]byte, 1024*1024)
	rand.Read(plaintext)

	b.Run("Encrypt", func(b *testing.B) {
		b.ResetTimer()
		b.SetBytes(1024 * 1024)
		for i := 0; i < b.N; i++ {
			_, err := cc.EncryptFileEnvelope(plaintext, 1)
			if err != nil {
				b.Fatalf("Encryption failed: %v", err)
			}
		}
	})

	ciphertext, _ := cc.EncryptFileEnvelope(plaintext, 1)

	b.Run("Decrypt", func(b *testing.B) {
		b.ResetTimer()
		b.SetBytes(1024 * 1024)
		for i := 0; i < b.N; i++ {
			_, err := cc.DecryptFileEnvelope(ciphertext)
			if err != nil {
				b.Fatalf("Decryption failed: %v", err)
			}
		}
	})
}

// BenchmarkDEKOnly_LargeFiles đo performance mã hóa files lớn (10MB)
func BenchmarkDEKOnly_LargeFiles(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	plaintext := make([]byte, 10*1024*1024)
	rand.Read(plaintext)

	b.Run("Encrypt", func(b *testing.B) {
		b.ResetTimer()
		b.SetBytes(10 * 1024 * 1024)
		for i := 0; i < b.N; i++ {
			_, err := cc.EncryptFileEnvelope(plaintext, 1)
			if err != nil {
				b.Fatalf("Encryption failed: %v", err)
			}
		}
	})

	ciphertext, _ := cc.EncryptFileEnvelope(plaintext, 1)

	b.Run("Decrypt", func(b *testing.B) {
		b.ResetTimer()
		b.SetBytes(10 * 1024 * 1024)
		for i := 0; i < b.N; i++ {
			_, err := cc.DecryptFileEnvelope(ciphertext)
			if err != nil {
				b.Fatalf("Decryption failed: %v", err)
			}
		}
	})
}

// BenchmarkDEKOnly_FilenameEncryption đo performance mã hóa filename
func BenchmarkDEKOnly_FilenameEncryption(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	filename := "test-filename-for-benchmark.txt"

	b.Run("Encrypt", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := cc.EncryptFilename(filename, "./")
			if err != nil {
				b.Fatalf("Filename encryption failed: %v", err)
			}
		}
	})

	encryptedFilename, _ := cc.EncryptFilename(filename, "./")

	b.Run("Decrypt", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := cc.DecryptFilename(encryptedFilename, "./")
			if err != nil {
				b.Fatalf("Filename decryption failed: %v", err)
			}
		}
	})
}

// BenchmarkDEKOnly_MemoryAllocation đo memory allocation patterns
func BenchmarkDEKOnly_MemoryAllocation(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	b.Run("SmallFile_1KB", func(b *testing.B) {
		plaintext := make([]byte, 1024)
		rand.Read(plaintext)

		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			ciphertext, err := cc.EncryptFileEnvelope(plaintext, 1)
			if err != nil {
				b.Fatalf("Encryption failed: %v", err)
			}
			_, err = cc.DecryptFileEnvelope(ciphertext)
			if err != nil {
				b.Fatalf("Decryption failed: %v", err)
			}
		}
	})

	b.Run("MediumFile_100KB", func(b *testing.B) {
		plaintext := make([]byte, 100*1024)
		rand.Read(plaintext)

		b.ReportAllocs()
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			ciphertext, err := cc.EncryptFileEnvelope(plaintext, 1)
			if err != nil {
				b.Fatalf("Encryption failed: %v", err)
			}
			_, err = cc.DecryptFileEnvelope(ciphertext)
			if err != nil {
				b.Fatalf("Decryption failed: %v", err)
			}
		}
	})
}

// BenchmarkDEKOnly_ConcurrentOperations đo performance với concurrent operations
func BenchmarkDEKOnly_ConcurrentOperations(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	plaintext := make([]byte, 4096)
	rand.Read(plaintext)

	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			ciphertext, err := cc.EncryptFileEnvelope(plaintext, 1)
			if err != nil {
				b.Fatalf("Encryption failed: %v", err)
			}
			_, err = cc.DecryptFileEnvelope(ciphertext)
			if err != nil {
				b.Fatalf("Decryption failed: %v", err)
			}
		}
	})
}

// BenchmarkDEKOnly_EnvelopeOperations đo performance envelope operations
func BenchmarkDEKOnly_EnvelopeOperations(b *testing.B) {
	masterKey := make([]byte, 32)
	rand.Read(masterKey)

	cc, err := NewCryptoCore(masterKey, 1)
	if err != nil {
		b.Fatalf("Cannot create crypto core: %v", err)
	}

	b.Run("CreateHeader", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := cc.CreateEnvelopeFileHeader(1)
			if err != nil {
				b.Fatalf("Cannot create header: %v", err)
			}
		}
	})

	header, _ := cc.CreateEnvelopeFileHeader(1)
	headerBytes := header.SerializeEnvelopeFileHeader()

	b.Run("ParseHeader", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := ParseEnvelopeFileHeader(headerBytes)
			if err != nil {
				b.Fatalf("Cannot parse header: %v", err)
			}
		}
	})

	dek := make([]byte, DEK_SIZE)
	rand.Read(dek)
	iv := make([]byte, DEK_IV_SIZE)
	rand.Read(iv)

	b.Run("encryptDEK", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := cc.encryptDEK(dek, iv)
			if err != nil {
				b.Fatalf("Cannot encrypt DEK: %v", err)
			}
		}
	})

	encryptedDEK, _ := cc.encryptDEK(dek, iv)

	b.Run("decryptDEK", func(b *testing.B) {
		b.ResetTimer()
		for i := 0; i < b.N; i++ {
			_, err := cc.decryptDEK(encryptedDEK, iv)
			if err != nil {
				b.Fatalf("Cannot decrypt DEK: %v", err)
			}
		}
	})
}
