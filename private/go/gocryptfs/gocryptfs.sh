#!/bin/bash

# gocryptfs Management Script
# Unified script for all gocryptfs operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_step() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to show usage
show_usage() {
    cat << EOF
gocryptfs Management Script - Unified Tool

Usage: $0 <category> <command> [options]

Categories:
    demo        Demo and testing operations
    test        Test suite operations  
    ui          Web interface operations
    build       Build and development operations

Demo Commands:
    demo setup          Setup demo environment
    demo init           Initialize encrypted filesystem
    demo mount          Mount and demonstrate operations
    demo dek            Run DEK-only demonstration
    demo clean          Clean up demo files

Test Commands:
    test unit           Run unit tests only
    test integration    Run integration tests only
    test security       Run security tests only
    test benchmarks     Run benchmark tests only
    test all            Run all tests
    test --verbose      Run with verbose output
    test --coverage     Generate coverage report

UI Commands:
    ui start            Start web interface
    ui stop             Stop web interface
    ui status           Check UI status

Build Commands:
    build binary        Build gocryptfs binary
    build clean         Clean build artifacts
    build deps          Install dependencies

Global Options:
    -h, --help          Show this help message
    -v, --verbose       Enable verbose output

Examples:
    $0 demo setup               # Setup demo environment
    $0 demo dek --keep          # Run DEK demo and keep mounted
    $0 test all --coverage      # Run all tests with coverage
    $0 ui start                 # Start web interface
    $0 build binary             # Build gocryptfs binary

Quick Start:
    $0 demo setup && $0 demo init && $0 demo mount
EOF
}

# Function to run demo operations
run_demo() {
    local command="$1"
    shift
    
    case "$command" in
        setup|init|mount|dek|clean)
            ./demo.sh "$command" "$@"
            ;;
        *)
            print_error "Unknown demo command: $command"
            echo "Available: setup, init, mount, dek, clean"
            exit 1
            ;;
    esac
}

# Function to run test operations
run_test() {
    if [ -f "tests/run_tests.sh" ]; then
        ./tests/run_tests.sh "$@"
    else
        print_error "Test script not found at tests/run_tests.sh"
        exit 1
    fi
}

# Function to run UI operations
run_ui() {
    local command="$1"
    shift
    
    case "$command" in
        start)
            if [ -f "ui/start-ui.sh" ]; then
                cd ui && ./start-ui.sh && cd ..
            else
                print_error "UI script not found at ui/start-ui.sh"
                exit 1
            fi
            ;;
        stop)
            print_step "Stopping UI services..."
            pkill -f "npm start" 2>/dev/null || true
            pkill -f "node.*3000" 2>/dev/null || true
            pkill -f "node.*3001" 2>/dev/null || true
            print_success "UI services stopped"
            ;;
        status)
            print_step "Checking UI status..."
            if lsof -i :3000 >/dev/null 2>&1; then
                print_success "Frontend running on port 3000"
            else
                print_warning "Frontend not running on port 3000"
            fi
            if lsof -i :3001 >/dev/null 2>&1; then
                print_success "Backend running on port 3001"
            else
                print_warning "Backend not running on port 3001"
            fi
            ;;
        *)
            print_error "Unknown UI command: $command"
            echo "Available: start, stop, status"
            exit 1
            ;;
    esac
}

# Function to run build operations
run_build() {
    local command="$1"
    shift
    
    case "$command" in
        binary)
            print_step "Building gocryptfs binary..."
            make build
            print_success "Binary built successfully"
            ;;
        clean)
            print_step "Cleaning build artifacts..."
            make clean 2>/dev/null || rm -rf build/
            print_success "Build artifacts cleaned"
            ;;
        deps)
            print_step "Installing dependencies..."
            go mod tidy
            go mod download
            if [ -d "ui" ]; then
                cd ui && npm install && cd ..
            fi
            if [ -d "ui/backend" ]; then
                cd ui/backend && npm install && cd ..
            fi
            print_success "Dependencies installed"
            ;;
        *)
            print_error "Unknown build command: $command"
            echo "Available: binary, clean, deps"
            exit 1
            ;;
    esac
}

# Main execution
if [ $# -eq 0 ]; then
    show_usage
    exit 0
fi

case "$1" in
    demo)
        shift
        run_demo "$@"
        ;;
    test)
        shift
        run_test "$@"
        ;;
    ui)
        shift
        run_ui "$@"
        ;;
    build)
        shift
        run_build "$@"
        ;;
    -h|--help|help)
        show_usage
        ;;
    *)
        print_error "Unknown category: $1"
        echo ""
        show_usage
        exit 1
        ;;
esac
