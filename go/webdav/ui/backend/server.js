const express = require('express');
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');
const cors = require('cors');

const app = express();
const PORT = 3001;

// Path to the WebDAV demo executable
const WEBDAV_DEMO_PATH = path.join(__dirname, '../../demo');
const PID_FILE_PATH = path.join(__dirname, '../../webdav.pid');

// Middleware
app.use(cors());
app.use(express.json());

// Helper function to check if process is running
function isProcessRunning(pid) {
  try {
    process.kill(pid, 0);
    return true;
  } catch (e) {
    return false;
  }
}

// Helper function to get server status
function getServerStatus() {
  try {
    if (!fs.existsSync(PID_FILE_PATH)) {
      return 'stopped';
    }
    
    const pidContent = fs.readFileSync(PID_FILE_PATH, 'utf8');
    const pid = parseInt(pidContent.trim());
    
    if (isNaN(pid)) {
      return 'stopped';
    }
    
    if (isProcessRunning(pid)) {
      return 'running';
    } else {
      // Clean up stale PID file
      fs.unlinkSync(PID_FILE_PATH);
      return 'stopped';
    }
  } catch (error) {
    console.error('Error checking server status:', error);
    return 'unknown';
  }
}

// API Routes

// Get server status
app.get('/api/status', (req, res) => {
  try {
    const status = getServerStatus();
    res.json({ status });
  } catch (error) {
    console.error('Error getting status:', error);
    res.status(500).json({ error: 'Failed to get server status' });
  }
});

// Start WebDAV server
app.post('/api/start', (req, res) => {
  try {
    const currentStatus = getServerStatus();
    
    if (currentStatus === 'running') {
      return res.status(400).json({ error: 'Server is already running' });
    }
    
    // Execute the WebDAV demo with --open flag
    const command = `cd "${path.dirname(WEBDAV_DEMO_PATH)}" && ./demo --open`;
    
    exec(command, { detached: true, stdio: 'ignore' }, (error, stdout, stderr) => {
      if (error) {
        console.error('Error starting server:', error);
        return;
      }
    });
    
    // Wait a moment for the server to start
    setTimeout(() => {
      const status = getServerStatus();
      if (status === 'running') {
        res.json({ 
          success: true, 
          message: 'WebDAV server started successfully',
          status: 'running'
        });
      } else {
        res.status(500).json({ error: 'Failed to start WebDAV server' });
      }
    }, 2000);
    
  } catch (error) {
    console.error('Error starting server:', error);
    res.status(500).json({ error: 'Failed to start WebDAV server' });
  }
});

// Stop WebDAV server
app.post('/api/stop', (req, res) => {
  try {
    const currentStatus = getServerStatus();
    
    if (currentStatus === 'stopped') {
      return res.status(400).json({ error: 'Server is already stopped' });
    }
    
    // Execute the WebDAV demo with --close flag
    const command = `cd "${path.dirname(WEBDAV_DEMO_PATH)}" && ./demo --close`;
    
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error('Error stopping server:', error);
        return res.status(500).json({ error: 'Failed to stop WebDAV server' });
      }
      
      res.json({ 
        success: true, 
        message: 'WebDAV server stopped successfully',
        status: 'stopped'
      });
    });
    
  } catch (error) {
    console.error('Error stopping server:', error);
    res.status(500).json({ error: 'Failed to stop WebDAV server' });
  }
});

// Get server info
app.get('/api/info', (req, res) => {
  try {
    const status = getServerStatus();
    const info = {
      status,
      url: 'https://localhost:8443',
      mountPoint: '/Volumes/localhost',
      cipherDirectory: './cipher',
      demoPath: WEBDAV_DEMO_PATH,
      pidFile: PID_FILE_PATH
    };
    
    res.json(info);
  } catch (error) {
    console.error('Error getting server info:', error);
    res.status(500).json({ error: 'Failed to get server info' });
  }
});

// Health check
app.get('/api/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'WebDAV Controller API'
  });
});

// Error handling middleware
app.use((err, req, res, next) => {
  console.error('Unhandled error:', err);
  res.status(500).json({ error: 'Internal server error' });
});

// 404 handler
app.use((req, res) => {
  res.status(404).json({ error: 'API endpoint not found' });
});

// Start the server
app.listen(PORT, () => {
  console.log(`WebDAV Controller API server running on http://localhost:${PORT}`);
  console.log(`WebDAV demo path: ${WEBDAV_DEMO_PATH}`);
  console.log(`PID file path: ${PID_FILE_PATH}`);
  
  // Check initial server status
  const initialStatus = getServerStatus();
  console.log(`Initial WebDAV server status: ${initialStatus}`);
});

// Graceful shutdown
process.on('SIGINT', () => {
  console.log('\nShutting down API server...');
  process.exit(0);
});

process.on('SIGTERM', () => {
  console.log('\nShutting down API server...');
  process.exit(0);
});
